<template>
  <view class="modern-dashboard" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="dashboard-header">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航 -->
        <view class="top-nav">
          <view class="welcome-section">
            <!-- 角色切换按钮 -->
            <view class="role-switch-container">
              <view class="role-switch-btn" @click="switchToStaffMode">
                <text class="switch-text">切换到接单端</text>
                <u-icon name="arrow-right" color="#fff" size="12"></u-icon>
              </view>
            </view>

            <view class="store-info-row">
              <view class="store-name-container">
                <text class="store-name">{{ currentStoreName || '门店名称' }}</text>
                <view class="store-switch-btn" @click="showStoreSelection">
                  <text class="switch-text">切换门店</text>
                  <u-icon name="arrow-down" color="#fff" size="12"></u-icon>
                </view>
              </view>
              <!-- 版本过期提醒 -->
              <view class="version-expire-notice" v-if="versionExpireTime">
                <view class="expire-notice-content">
                  <view class="expire-dot"></view>
                  <text class="expire-text">最近到期：{{ formatExpireTime(versionExpireTime) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 快速状态卡片 -->
        <view class="quick-status">
          <view class="status-card user-info-card">
            <view class="user-avatar-container">
              <view class="user-avatar">
                <image src="https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png" mode="aspectFill"></image>
              </view>
              <view class="online-status"></view>
            </view>
            <view class="user-info">
              <view class="greeting-time">{{ greetingText }}</view>
              <view class="user-name-section">
                <text class="user-name">{{ (user && user.name) || '管理员' }}</text>
                <view class="user-role">{{ (user && user.role_name) || '店长' }}</view>
              </view>
            </view>
          </view>

          <view class="status-card qrcode-card" @click="showQRCodeModal">
            <view class="qrcode-icon-bg">
              <text class="qrcode-icon">⊞</text>
            </view>
            <view class="status-info">
              <text class="status-label">门店二维码</text>
              <text class="status-value">点击生成</text>
            </view>
          </view>
        </view>

        <!-- 余额卡片 -->
        <view class="balance-section">
          <view class="balance-card" @click="goUrl('余额管理')">
            <view class="balance-header">
              <view class="balance-title-section">
                <text class="balance-title">账户余额</text>
                <text class="balance-subtitle">点击查看详情</text>
              </view>
              <view class="balance-toggle" @click.stop="toggleBalanceVisibility">
                <u-icon :name="showBalance ? 'eye-off' : 'eye'" color="#666" size="20"></u-icon>
              </view>
            </view>
            <view class="balance-content">
              <view class="balance-amount">
                <text class="currency-symbol">￥</text>
                <text class="amount-value">{{ showBalance ? (balanceAmount || '0.00') : '****' }}</text>
              </view>
              <view class="balance-actions">
                <view class="action-btn recharge-btn" @click.stop="goUrl('余额管理')">
                  <u-icon name="plus" color="#fff" size="16"></u-icon>
                  <text>充值</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 数据概览区域 -->
    <view class="dashboard-content">
      <view class="data-overview">
        <view class="overview-header">
          <text class="section-title">业务概览</text>
          <view class="time-filter">
            <view class="filter-tab" :class="{ active: currentTab === 'week' }" @click="changeTab('week')">本周</view>
            <view class="filter-tab" :class="{ active: currentTab === 'month' }" @click="changeTab('month')">本月</view>
          </view>
        </view>

        <view class="overview-cards">
          <view class="overview-card" @click="goUrl('服务人员')">
            <view class="card-icon staff-icon">
              <u-icon name="account" color="#fff" size="28"></u-icon>
            </view>
            <view class="card-content">
              <text class="card-number">{{ getCurrentStats('staff') }}</text>
              <text class="card-label">服务人员</text>
            </view>
            <view class="card-trend" :class="getStaffTrend().type">
              <u-icon :name="getStaffTrend().icon || 'minus'" :color="getStaffTrend().color || '#999'" size="12"></u-icon>
              <text>{{ getStaffTrend().text || '0' }}</text>
            </view>
          </view>

          <view class="overview-card" @click="goUrl('客户列表')">
            <view class="card-icon customer-icon">
              <u-icon name="account-fill" color="#fff" size="28"></u-icon>
            </view>
            <view class="card-content">
              <text class="card-number">{{ getCurrentStats('customers') }}</text>
              <text class="card-label">客户数量</text>
            </view>
            <view class="card-trend" :class="getCustomerTrend().type">
              <u-icon :name="getCustomerTrend().icon || 'minus'" :color="getCustomerTrend().color || '#999'" size="12"></u-icon>
              <text>{{ getCustomerTrend().text || '0' }}</text>
            </view>
          </view>

          <view class="overview-card" @click="goUrl('订单列表')">
            <view class="card-icon order-icon">
              <u-icon name="list" color="#fff" size="28"></u-icon>
            </view>
            <view class="card-content">
              <text class="card-number">{{ getCurrentStats('orders') }}</text>
              <text class="card-label">订单数量</text>
            </view>
            <view class="card-trend" :class="getOrderTrend().type">
              <u-icon :name="getOrderTrend().icon || 'minus'" :color="getOrderTrend().color || '#999'" size="12"></u-icon>
              <text>{{ getOrderTrend().text || '0' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 通知滚动播报 -->
      <view class="notification-announcements" @click="goToLatestAnnouncement">
        <view class="notification-header">
          <view class="notification-icon">
            <u-icon name="volume" color="#fdd118" size="16"></u-icon>
          </view>
          <text class="notification-label">通知公告</text>
          <view class="more-link" @click.stop="goToAnnouncementList">
            更多 <text class="arrow">></text>
          </view>
        </view>
        <view class="notification-content">
          <view class="notification-scroll">
            <view class="notification-item" v-for="item in notificationList" :key="item.id">
              <text class="notification-title">{{ item.title }}</text>
              <text class="notification-desc">{{ item.content }}</text>
              <text class="time-info">{{ item.time }}</text>
            </view>
          </view>
        </view>
      </view>



      <!-- 核心功能区域 -->
      <view class="core-functions">
        <view class="section-header">
          <text class="section-title">核心功能</text>
        </view>

        <view class="function-grid">
          <view class="function-card" @click="goUrl('服务人员')">
            <view class="function-icon">
              <u-icon name="account" color="#fdd118" size="24"></u-icon>
            </view>
            <view class="function-info">
              <text class="function-title">服务人员</text>
              <text class="function-desc">管理服务团队</text>
            </view>
            <view class="function-arrow">
              <u-icon name="arrow-right" color="#666" size="16"></u-icon>
            </view>
          </view>

          <view class="function-card" @click="goUrl('客户列表')">
            <view class="function-icon">
              <u-icon name="account-fill" color="#09be89" size="24"></u-icon>
            </view>
            <view class="function-info">
              <text class="function-title">客户列表</text>
              <text class="function-desc">客户信息管理</text>
            </view>
            <view class="function-arrow">
              <u-icon name="arrow-right" color="#666" size="16"></u-icon>
            </view>
          </view>

          <view class="function-card" @click="goUrl('订单列表')">
            <view class="function-icon">
              <u-icon name="list" color="#ff801b" size="24"></u-icon>
            </view>
            <view class="function-info">
              <text class="function-title">订单列表</text>
              <text class="function-desc">订单状态跟踪</text>
            </view>
            <view class="function-arrow">
              <u-icon name="arrow-right" color="#666" size="16"></u-icon>
            </view>
          </view>

          <view class="function-card" @click="goUrl('设置')">
            <view class="function-icon">
              <u-icon name="setting" color="#666" size="24"></u-icon>
            </view>
            <view class="function-info">
              <text class="function-title">系统设置</text>
              <text class="function-desc">个性化配置</text>
            </view>
            <view class="function-arrow">
              <u-icon name="arrow-right" color="#666" size="16"></u-icon>
            </view>
          </view>

          <!-- 家政人广场按钮 - 占据两列宽度 -->
          <view class="function-card function-card-wide" @click="goUrl('家政人广场')">
            <view class="function-icon">
              <u-icon name="star" color="#ff6b35" size="24"></u-icon>
            </view>
            <view class="function-info">
              <text class="function-title">家政人广场</text>
              <text class="function-desc">推广家政服务</text>
            </view>
            <view class="function-arrow">
              <u-icon name="arrow-right" color="#666" size="16"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 轮播图区域 -->
      <view class="banner-section">
        <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500" circular="true">
          <swiper-item v-for="(banner, index) in bannerList" :key="banner.id" @click="goUrl(banner.link)">
            <view class="banner-item">
              <image :src="banner.image" mode="aspectFill" class="banner-image"></image>
              <view class="banner-overlay">
                <view class="banner-content">
                  <text class="banner-title">{{ banner.title }}</text>
                  <text class="banner-subtitle">{{ banner.subtitle }}</text>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 商家培训区域 -->
      <view class="training-section">
        <view class="section-header">
          <text class="section-title">商家培训</text>
          <view class="more-link" @click="goUrl('更多培训')">
            更多 <text class="arrow">></text>
          </view>
        </view>

        <view class="training-list">
          <view class="training-item" v-for="(item, index) in trainingCourses" :key="index" @click="goTrainingDetail(item)">
            <view class="training-info">
              <view class="training-title">{{ item.title }}</view>
              <view class="training-stats">{{ item.views }}人已学习</view>
            </view>
            <image :src="item.image" mode="aspectFill" class="training-image"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 门店选择弹窗 -->
    <view class="store-modal-mask" v-if="showStoreSelector" @click="showStoreSelector = false">
      <view class="store-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择门店</text>
          <view class="close-btn" @click="showStoreSelector = false">
            <text class="close-icon">×</text>
          </view>
        </view>
        <view class="modal-content">
          <view class="store-list">
            <view
              class="store-item"
              v-for="store in storeList"
              :key="store.id"
              @click="switchStore(store)"
              :class="{ active: isCurrentStore(store) }"
            >
              <view class="store-info">
                <view class="store-main">
                  <text class="store-name">{{ store.name }}</text>
                  <view v-if="isCurrentStore(store)" class="current-badge">
                    <text class="badge-text">当前</text>
                  </view>
                </view>
                <view class="store-details">
                  <text class="store-id">门店ID: {{ store.store_uuid }}</text>
                  <view class="store-status" :class="{ active: store.status === 1 }">
                    <view class="status-dot"></view>
                    <text class="status-text">{{ store.status === 1 ? '营业中' : '已关闭' }}</text>
                  </view>
                </view>
              </view>
              <view class="store-action">
                <view v-if="isCurrentStore(store)" class="check-icon">
                  <u-icon name="checkmark" color="#09be89" size="20"></u-icon>
                </view>
                <view v-else class="switch-icon">
                  <u-icon name="arrow-right" color="#999" size="16"></u-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 小程序码弹窗 -->
    <view class="qrcode-modal-mask" v-if="showQRModal" @click="showQRModal = false">
      <view class="qrcode-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">门店小程序码</text>
          <view class="close-btn" @click="showQRModal = false">
            <text class="close-icon">×</text>
          </view>
        </view>

        <view class="modal-content">
          <text class="modal-subtitle">分享给客户，快速下单</text>

          <view class="qrcode-display">
            <view class="qrcode-preview" v-if="qrcodeUrl">
              <image :src="qrcodeUrl" mode="scaleToFill" class="qrcode-image" show-menu-by-longpress="true"></image>
              <text class="qrcode-tips">长按保存或识别二维码</text>
            </view>

            <view class="qrcode-placeholder" v-else @click="generateQRCode">
              <view class="placeholder-icon">
                <text class="placeholder-qr">⊞</text>
              </view>
              <text class="placeholder-text">点击生成小程序码</text>
            </view>
          </view>


        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { userInfo, getDashboardInitData } from '@/api/user.js';
import { generateCustomMiniQr, getStoreMiniQrCode } from '@/api/common.js';
import { getPublicTrainingCourses } from '@/api/training.js';
import { getHomeAnnouncements, getLatestAnnouncement } from '@/api/announcement.js';
// import { checkStaffAccount, switchToStaffAccount } from '@/api/staff-auth';
export default {
  components: {
  },
  data() {
    return {
      fixed: false,
      currentTab: 'week',
      showMoney: false,
      money: 1000,
      capsuleHeight: 10,
      // 新增数据属性
      hasNotification: true,
      showQRModal: false,
      // 余额相关数据
      showBalance: true, // 是否显示余额
      balanceAmount: '0.00', // 余额金额，从接口获取
      currentUser: {
        name: '管理员',
        avatar: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png'
      },
      statsData: {
        // 原始统计数据
        orders_this_week: 0,
        orders_this_month: 0,
        staff_this_week: 0,
        staff_this_month: 0,
        total_staff: 0,
        customers_this_week: 0,
        customers_this_month: 0,
        total_customers: 0,
        // 周增长数据
        orders_week_growth: 0,
        orders_week_growth_rate: 0.0,
        staff_week_growth: 0,
        staff_week_growth_rate: 0.0,
        customers_week_growth: 0,
        customers_week_growth_rate: 0.0,
        // 月增长数据
        orders_month_growth: 0,
        orders_month_growth_rate: 0.0,
        staff_month_growth: 0,
        staff_month_growth_rate: 0.0,
        customers_month_growth: 0,
        customers_month_growth_rate: 0.0
      },
      // 当前门店信息
      currentStore: null,
      // 小程序码相关数据
      qrcodeUrl: '',
      generating: false,
      // 培训课程数据
      trainingCourses: [],
      // 轮播图数据
      bannerList: [
        {
          id: 1,
          title: '专业家政人服务',
          // subtitle: '让生活更美好',
          image: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/%E9%A6%96%E9%A1%B52.png',
          link: '/pages/home/<USER>'
        },
        {
          id: 2,
          title: '成就家政人',
          // subtitle: '专业团队上门服务',
          image: 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/%E9%A6%96%E9%A1%B5%E8%83%8C%E6%99%AF.png',
          link: '/pages/home/<USER>'
        },

      ],
      // 通知滚动播报数据
      notificationList: [],
      // 门店信息
      currentStoreName: '',
      storeList: [],
      showStoreSelector: false,
      // 版本过期时间
      versionExpireTime: null
    };
  },
  computed: {
    ...mapState(['sysTopBarH', 'StatusBar', 'isX', 'token', 'storeInfo', 'staffInfo', 'currentRole', 'user']),
    greetingText() {
      const hour = new Date().getHours();
      if (hour < 6) {
        return '夜深了，注意休息';
      } else if (hour < 9) {
        return '早上好';
      } else if (hour < 12) {
        return '上午好';
      } else if (hour < 14) {
        return '中午好';
      } else if (hour < 18) {
        return '下午好';
      } else if (hour < 22) {
        return '晚上好';
      } else {
        return '夜深了，注意休息';
      }
    }
  },

  onLoad() {},
  onShow() {
    console.log('首页显示，刷新数据');
    this.$nextTick(() => {
      if (!this.token) {
        return uni.navigateTo({ url: './login' });
      }

      // 检查用户角色，如果是员工则跳转到员工端首页
      this.checkUserRoleAndRedirect();
    });
  },

  // 监听页面滚动
  onPageScroll(e) {
    this.fixed = e.scrollTop > 100;
  },

  // 微信分享配置
  onShareAppMessage() {
    console.log('=== 触发分享 ===');

    let shareobj = {
      title: '家政服务好帮手，进来逛逛吧~', //分享的标题
      path: '/pages/login/login?tg=' + uni.getStorageSync('tg') + '&shareScene=' + uni.getStorageSync('scene'), //好友点击分享之后跳转的页面
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png", //分享的图片
    };
    return shareobj;
  },

  methods: {
    // 检查用户角色并重定向
    checkUserRoleAndRedirect() {
      try {
        // 从storage中获取用户角色和相关信息
        const currentRole = uni.getStorageSync('currentRole');
        const staffInfo = uni.getStorageSync('staffInfo');
        const staffToken = uni.getStorageSync('staffToken');
        const storeInfo = uni.getStorageSync('storeInfo');
        const token = uni.getStorageSync('token');

        console.log('=== 门店端角色检查 ===');
        console.log('当前角色:', currentRole);
        console.log('员工信息:', staffInfo ? '存在' : '不存在');
        console.log('员工Token:', staffToken ? '存在' : '不存在');
        console.log('门店信息:', storeInfo ? '存在' : '不存在');
        console.log('门店Token:', token ? '存在' : '不存在');

        // 优先基于currentRole判断，只有明确设置为staff时才跳转
        if (currentRole === 'staff') {
          console.log('当前角色为员工端，跳转到员工端首页');
          uni.reLaunch({
            url: '/pages-staff/home/<USER>'
          });
          return;
        }

        // 如果没有设置角色但有员工信息，且没有门店信息，则跳转到员工端
        if (!currentRole && staffInfo && !storeInfo) {
          console.log('检测到仅有员工身份，跳转到员工端首页');
          uni.setStorageSync('currentRole', 'staff');
          this.$store.commit('Updates', { currentRole: 'staff' });
          uni.reLaunch({
            url: '/pages-staff/home/<USER>'
          });
          return;
        }

        // 其他情况继续加载门店端数据
        console.log('门店端用户，继续加载首页数据');
        this.loadUserInfo();
      } catch (error) {
        console.error('检查用户角色失败:', error);
        // 如果检查失败，默认加载管理端数据
        this.loadUserInfo();
      }
    },

    // 格式化过期时间
    formatExpireTime(timeStr) {
      if (!timeStr) return '';
      try {
        // 将 "yyyy-MM-dd HH:mm:ss" 格式转换为 iOS 兼容的格式
        const isoTimeStr = timeStr.replace(' ', 'T');
        const date = new Date(isoTimeStr);

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的日期格式:', timeStr);
          return timeStr;
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('格式化时间失败:', error);
        return timeStr;
      }
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        // 首先尝试调用新的首页初始化接口
        const dashboardData = await getDashboardInitData();
        console.log('首页初始化数据:', dashboardData);

        if (dashboardData) {
          // 更新当前门店信息 - 数据直接在根级别，不需要通过.data访问
          if (dashboardData.current_store) {
            this.currentStore = dashboardData.current_store;
            this.currentStoreName = dashboardData.current_store.name;
            console.log('当前门店信息:', dashboardData.current_store);

            // 更新公司余额
            if (dashboardData.current_store.company_balance) {
              this.balanceAmount = dashboardData.current_store.company_balance;
              console.log('更新公司余额:', this.balanceAmount);
            }

            // 将门店信息存储到Vuex和本地存储
            this.$store.commit('Updates', { storeInfo: dashboardData.current_store });
            uni.setStorageSync('storeInfo', dashboardData.current_store);
            console.log('门店信息已存储到Vuex和本地存储，包含is_xyj字段:', dashboardData.current_store.is_xyj);
          }

          // 更新门店列表 - 数据直接在根级别，不需要通过.data访问
          if (dashboardData.store_list && dashboardData.store_list.length > 0) {
            this.storeList = dashboardData.store_list;
          } else if (dashboardData.current_store) {
            // 如果store_list为空但有current_store，使用current_store构建列表
            this.storeList = [dashboardData.current_store];
          }

          // 更新统计数据 - 数据直接在根级别，不需要通过.data访问
          if (dashboardData.statistics) {
            this.statsData = {
              orders_this_week: dashboardData.statistics.orders_this_week || 0,
              orders_this_month: dashboardData.statistics.orders_this_month || 0,
              staff_this_week: dashboardData.statistics.staff_this_week || 0,
              staff_this_month: dashboardData.statistics.staff_this_month || 0,
              total_staff: dashboardData.statistics.total_staff || 0,
              customers_this_week: dashboardData.statistics.customers_this_week || 0,
              customers_this_month: dashboardData.statistics.customers_this_month || 0,
              total_customers: dashboardData.statistics.total_customers || 0,
              // 周增长数据
              orders_week_growth: dashboardData.statistics.orders_week_growth || 0,
              orders_week_growth_rate: dashboardData.statistics.orders_week_growth_rate || 0.0,
              staff_week_growth: dashboardData.statistics.staff_week_growth || 0,
              staff_week_growth_rate: dashboardData.statistics.staff_week_growth_rate || 0.0,
              customers_week_growth: dashboardData.statistics.customers_week_growth || 0,
              customers_week_growth_rate: dashboardData.statistics.customers_week_growth_rate || 0.0,
              // 月增长数据
              orders_month_growth: dashboardData.statistics.orders_month_growth || 0,
              orders_month_growth_rate: dashboardData.statistics.orders_month_growth_rate || 0.0,
              staff_month_growth: dashboardData.statistics.staff_month_growth || 0,
              staff_month_growth_rate: dashboardData.statistics.staff_month_growth_rate || 0.0,
              customers_month_growth: dashboardData.statistics.customers_month_growth || 0,
              customers_month_growth_rate: dashboardData.statistics.customers_month_growth_rate || 0.0
            };
          }

          // 更新版本过期时间
          if (dashboardData.version_expire_time) {
            this.versionExpireTime = dashboardData.version_expire_time;
          }

          // 加载通知数据
          this.loadNotifications();

          // 加载培训课程数据
          this.loadTrainingCourses();
        }
      } catch (error) {
        console.error('获取首页初始化数据失败，回退到原有接口:', error);

        // 回退到原有的userInfo接口
        try {
          const res = await userInfo();
          console.log('getinfo返回的数据:', res);
          if (res && res.user) {
            // 更新门店名称 - 优先从user.store_name获取
            if (res.user.store_name) {
              this.currentStoreName = res.user.store_name;
              console.log('从user.store_name获取门店名称:', res.user.store_name);
            } else if (res.company_info && res.company_info.name) {
              this.currentStoreName = res.company_info.name;
              console.log('从company_info.name获取门店名称:', res.company_info.name);
            } else {
              console.log('未找到门店名称，使用默认值');
            }

            // 更新门店列表
            if (res.store_list && res.store_list.length > 0) {
              this.storeList = res.store_list;
              console.log('门店列表:', res.store_list);
            }

            // 加载通知数据
            this.loadNotifications();

            // 加载培训课程数据
            this.loadTrainingCourses();
          }
        } catch (fallbackError) {
          console.error('获取用户信息失败:', fallbackError);
        }
      }
    },

    // 加载通知数据
    async loadNotifications() {
      try {
        console.log('开始加载通知公告数据');
        const result = await getHomeAnnouncements({ limit: 5 });
        console.log('通知公告API返回数据:', result);

        if (result && result.length > 0) {
          this.notificationList = result;
          console.log('通知公告数据加载成功:', this.notificationList);
        } else {
          console.log('暂无通知公告数据，使用默认数据');
          // 如果API没有数据，使用默认数据
          this.notificationList = [
            { id: 1, title: '金刚到家', content: '一个能提供订单的平台', time: '刚刚' },
            { id: 2, title: '省钱', content: '自动化提升人效', time: '5分钟前' },
            { id: 3, title: '引流', content: '全渠道流量推广', time: '10分钟前' },
            { id: 4, title: '私域', content: '高毛利产品复购', time: '15分钟前' }
          ];
        }
      } catch (error) {
        console.error('加载通知公告数据失败:', error);
        // 如果API调用失败，使用默认数据
        this.notificationList = [
          { id: 1, title: '金刚到家', content: '一个能提供订单的平台', time: '刚刚' },
          { id: 2, title: '省钱', content: '自动化提升人效', time: '5分钟前' },
          { id: 3, title: '引流', content: '全渠道流量推广', time: '10分钟前' },
          { id: 4, title: '私域', content: '高毛利产品复购', time: '15分钟前' }
        ];
      }
    },

    // 加载培训课程数据
    async loadTrainingCourses() {
      try {
        console.log('开始加载培训课程数据');
        const result = await getPublicTrainingCourses({ limit: 10 });
        console.log('培训课程API返回数据:', result);

        if (result && result.list && result.list.length > 0) {
          // 将API返回的数据映射到前端需要的格式
          this.trainingCourses = result.list.map(course => ({
            id: course.id,
            uuid: course.uuid,
            title: course.title,
            views: course.views || 0,
            image: course.cover_image || 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/%E9%A6%96%E9%A1%B52.png',
            videoUrl: this.extractVideoUrl(course.content_data),
            description: course.description,
            content_type: course.content_type,
            duration: course.duration
          }));
          console.log('培训课程数据加载成功:', this.trainingCourses);
        } else {
          console.log('暂无培训课程数据');
          this.trainingCourses = [];
        }
      } catch (error) {
        console.error('加载培训课程数据失败:', error);
        // 如果API调用失败，使用默认数据
        this.trainingCourses = [];
      }
    },

    // 从content_data中提取视频URL
    extractVideoUrl(contentData) {
      try {
        if (!contentData) return '';

        // 如果contentData是字符串，尝试解析为JSON
        let data = contentData;
        if (typeof contentData === 'string') {
          data = JSON.parse(contentData);
        }

        // 根据不同的数据结构提取视频URL
        if (data.video_url) {
          return data.video_url;
        } else if (data.url) {
          return data.url;
        } else if (data.src) {
          return data.src;
        } else if (Array.isArray(data) && data.length > 0 && data[0].url) {
          return data[0].url;
        }

        return '';
      } catch (error) {
        console.error('解析视频URL失败:', error);
        return '';
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '刚刚';
      const time = new Date(timeStr);
      const now = new Date();
      const diff = now - time;

      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
      return Math.floor(diff / 86400000) + '天前';
    },

    // 显示门店选择器
    showStoreSelection() {
      console.log('=== 点击切换门店按钮 ===');
      console.log('当前门店列表:', this.storeList);
      console.log('当前showStoreSelector状态:', this.showStoreSelector);

      // 始终显示门店选择器，如果没有门店列表则显示默认门店
      if (!this.storeList || this.storeList.length === 0) {
        this.storeList = [{ id: 1, name: '金刚到家', store_uuid: 'default' }];
        console.log('创建默认门店列表:', this.storeList);
      }
      this.showStoreSelector = true;
      console.log('设置showStoreSelector为true，当前值:', this.showStoreSelector);

      // 使用$nextTick确保DOM更新
      this.$nextTick(() => {
        console.log('$nextTick中的showStoreSelector状态:', this.showStoreSelector);
      });
    },

    // 切换门店
    switchStore(store) {
      console.log('=== 切换门店 ===');
      console.log('选择的门店:', store);

      // 如果选择的是当前门店，直接关闭弹窗
      if (this.isCurrentStore(store)) {
        this.showStoreSelector = false;
        return;
      }

      // 更新当前门店信息
      this.currentStore = store;
      this.currentStoreName = store.name;
      this.showStoreSelector = false;

      console.log('门店切换完成，当前门店:', this.currentStore);

      // 显示切换成功提示
      uni.showToast({
        title: `已切换到${store.name}`,
        icon: 'success'
      });

      // 这里可以添加切换门店后的逻辑，比如重新加载数据
      // this.loadUserInfo();
    },

    // 切换本周、本月
    changeTab(tab) {
      this.currentTab = tab;
      console.log(`切换到${tab === 'week' ? '本周' : '本月'}数据`);
    },

    // 获取当前选中时间段的统计数据
    getCurrentStats(type) {
      if (this.currentTab === 'week') {
        switch (type) {
          case 'staff':
            return this.statsData.staff_this_week || 0;
          case 'customers':
            return this.statsData.customers_this_week || 0;
          case 'orders':
            return this.statsData.orders_this_week || 0;
          default:
            return 0;
        }
      } else {
        switch (type) {
          case 'staff':
            return this.statsData.staff_this_month || 0;
          case 'customers':
            return this.statsData.customers_this_month || 0;
          case 'orders':
            return this.statsData.orders_this_month || 0;
          default:
            return 0;
        }
      }
    },

    // 获取员工趋势
    getStaffTrend() {
      if (!this.statsData) {
        return {
          type: String('stable'),
          icon: String('minus'),
          color: String('#999'),
          text: String('0')
        };
      }

      const growth = this.currentTab === 'week'
        ? this.statsData.staff_week_growth || 0
        : this.statsData.staff_month_growth || 0;

      if (growth > 0) {
        return {
          type: String('up'),
          icon: String('arrow-up'),
          color: String('#52c41a'),
          text: String(`+${growth}`)
        };
      } else if (growth < 0) {
        return {
          type: String('down'),
          icon: String('arrow-down'),
          color: String('#ff4d4f'),
          text: String(`${growth}`)
        };
      } else {
        return {
          type: String('stable'),
          icon: String('minus'),
          color: String('#999'),
          text: String('0')
        };
      }
    },

    // 获取客户趋势
    getCustomerTrend() {
      if (!this.statsData) {
        return {
          type: String('stable'),
          icon: String('minus'),
          color: String('#999'),
          text: String('0')
        };
      }

      const growth = this.currentTab === 'week'
        ? this.statsData.customers_week_growth || 0
        : this.statsData.customers_month_growth || 0;

      if (growth > 0) {
        return {
          type: String('up'),
          icon: String('arrow-up'),
          color: String('#52c41a'),
          text: String(`+${growth}`)
        };
      } else if (growth < 0) {
        return {
          type: String('down'),
          icon: String('arrow-down'),
          color: String('#ff4d4f'),
          text: String(`${growth}`)
        };
      } else {
        return {
          type: String('stable'),
          icon: String('minus'),
          color: String('#999'),
          text: String('0')
        };
      }
    },

    // 获取订单趋势
    getOrderTrend() {
      if (!this.statsData) {
        return {
          type: String('stable'),
          icon: String('minus'),
          color: String('#999'),
          text: String('0')
        };
      }

      const growth = this.currentTab === 'week'
        ? this.statsData.orders_week_growth || 0
        : this.statsData.orders_month_growth || 0;

      if (growth > 0) {
        return {
          type: String('up'),
          icon: String('arrow-up'),
          color: String('#52c41a'),
          text: String(`+${growth}`)
        };
      } else if (growth < 0) {
        return {
          type: String('down'),
          icon: String('arrow-down'),
          color: String('#ff4d4f'),
          text: String(`${growth}`)
        };
      } else {
        return {
          type: String('stable'),
          icon: String('minus'),
          color: String('#999'),
          text: String('0')
        };
      }
    },

    // 判断是否为当前门店
    isCurrentStore(store) {
      return this.currentStore && store.store_uuid === this.currentStore.store_uuid;
    },

    // 显示小程序码弹窗
    async showQRCodeModal() {
      console.log('=== showQRCodeModal 方法被调用 ===');
      console.log('点击前 showQRModal 状态:', this.showQRModal);

      this.showQRModal = true;

      console.log('点击后 showQRModal 状态:', this.showQRModal);

      // 使用 $nextTick 确保DOM更新
      this.$nextTick(() => {
        console.log('$nextTick 中的 showQRModal 状态:', this.showQRModal);
      });

      // 显示加载提示
      uni.showLoading({
        title: '正在加载门店码...',
        mask: true
      });

      // 自动获取门店小程序码
      await this.loadStoreMiniQrCode();

      console.log('=== showQRCodeModal 方法执行完毕 ===');
    },

    // 加载门店小程序码
    async loadStoreMiniQrCode() {
      console.log('=== 开始加载门店小程序码 ===');

      try {
        // 获取当前门店UUID
        const storeUuid = this.user?.store_uuid || this.currentStore?.store_uuid;

        if (!storeUuid) {
          console.error('门店UUID不存在');
          uni.hideLoading();
          uni.showToast({
            title: '门店信息不完整',
            icon: 'none'
          });
          return;
        }

        console.log('当前门店UUID:', storeUuid);

        // 调用获取门店小程序码接口
        const result = await getStoreMiniQrCode(storeUuid);

        console.log('获取门店小程序码结果:', result);

        if (result && result.qr_url) {
          this.qrcodeUrl = result.qr_url;
          console.log('门店小程序码加载成功:', this.qrcodeUrl);

          uni.hideLoading();
          if (result.is_new) {
            uni.showToast({
              title: '小程序码已生成',
              icon: 'success'
            });
          } else {
            uni.showToast({
              title: '门店码加载成功',
              icon: 'success'
            });
          }
        } else {
          console.error('获取门店小程序码失败:', result);
          // 如果获取失败，清空URL，让用户手动生成
          this.qrcodeUrl = '';
          uni.hideLoading();
          uni.showToast({
            title: '门店码加载失败，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('=== 加载门店小程序码失败 ===');
        console.error('错误详情:', error);
        // 如果获取失败，清空URL，让用户手动生成
        this.qrcodeUrl = '';
        uni.hideLoading();
        uni.showToast({
          title: '门店码加载失败，请重试',
          icon: 'none'
        });
      }

      console.log('=== 加载门店小程序码结束 ===');
    },

    // 菜单右上角+点击事件
    goUrl(val) {
      if (!val) return;
      console.log(val);
      if (val == '一键发单') {
        return uni.navigateTo({ url: '/pages/fast-create-order/fast-create-order' });
      }
      if (val == '合同') {
        return uni.navigateTo({ url: '/pages-agreement/agreement' });
      }
      if (val == '设置') {
        return uni.navigateTo({ url: '/pages-set/index' });
      }
      if (val == '客户列表') {
        // 跳转到底部导航的"到家"页面，并显示客户tab
        return uni.switchTab({
          url: '/pages/dispatch/index',
          success: () => {
            // 通过事件总线通知到家页面切换到客户tab
            uni.$emit('switchToClientTab');
          }
        });
      }
      if (val == '订单列表') {
        // 跳转到底部导航的"到家"页面，并显示订单tab
        return uni.switchTab({
          url: '/pages/dispatch/index',
          success: () => {
            // 通过事件总线通知到家页面切换到订单tab
            uni.$emit('switchToOrderTab');
          }
        });
      }
      if (val == '资金流水') {
        return uni.navigateTo({ url: '/pages-capital-flow/index' });
      }
      if (val == '余额管理') {
        return uni.navigateTo({
          url: '/pages-capital-flow/balance-manage',
          fail: (err) => {
            console.error('跳转失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
      if (val == '代客下单') {
        return uni.navigateTo({ url: '/pages-home/sub-po' });
      }
      if (val == '发优惠券') {
        return uni.navigateTo({ url: '/pages-home/promo' });
      }
      if (val == '服务人员') {
        // 跳转到底部导航的服务人员页面
        return uni.switchTab({ url: '/pages/staff/index' });
      }
      if (val == '简历') {
        return uni.navigateTo({ url: '/pages-home/attendantManage-add' });
      }
      if (val == '服务项目') {
        return uni.navigateTo({ url: '/pages-set/serviceProjects/index' });
      }
      if (val == '分销商品') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '招聘栏') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '档期表') {
        return uni.navigateTo({ url: '/pages/schedule/schedule' });
      }
      if (val == '客户') {
        return uni.navigateTo({ url: '/pages/fast-create-order/clue-info' });
      }
      if (val == '热文推广') {
        return uni.navigateTo({ url: '/pages-home/hot-text' });
      }
      if (val == '宣传海报') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '招生海报') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '营销文案') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '宣传视频') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '我的录播课') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '学员线索') {
        return uni.navigateTo({ url: '/pages-home/student-list' });
      }
      if (val == '收益管理') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '找阿姨') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '附近合单') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '业务统计') {
        return uni.navigateTo({ url: '/pages-home/total' });
      }
      if (val == '美团新单') {
        return uni.navigateTo({ url: '/pages-home/meituan-order' });
      }
      if (val == '抖音新单') {
        return uni.navigateTo({ url: '/pages-home/douyin-order' });
      }
      if (val == '自主填写') {
        return uni.navigateTo({ url: '/pages-home/self-write' });
      }
      if (val == '技能培训') {
        return uni.navigateTo({ url: '/pages-home/skill-training' });
      }
      if (val == '心理测评') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '星选线索') {
        return uni.navigateTo({ url: '/pages-home/star-leads' });
      }
      if (val == '微网站') {
        return uni.navigateTo({ url: '/pages-home/website' });
      }
      if (val == '签到管理') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '我的网校') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '自主填需求') {
        return uni.navigateTo({ url: '/pages-home/demand-form' });
      }
      if (val == '远程面试') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '家政保险') {
        return uni.navigateTo({ url: '/pages-home/insurance' });
      }
      if (val == '家政体检') {
        return uni.navigateTo({ url: '/pages-home/physical/index' });
      }
      if (val == '考试报名') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '记账收款') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '入驻商务部') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '售后/投诉') {
        return uni.navigateTo({ url: '/pages-home/online-service' });
      }
      if (val == '入住百度高德') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      if (val == '功能介绍') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }

      if (val == '通知') {
        return uni.navigateTo({ url: '/pages/notification/notification-list' });
      }

      if (val == '代办') {
        return uni.navigateTo({ url: '/pages/todo/todo-list' });
      }
      if (val == '全部') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }
      console.log(111);

      if (val === '短信营销') {
        return uni.navigateTo({
          url: '/pages/home/<USER>',
          success: () => {
            console.log('跳转成功');
          },
          fail: err => {
            console.error('跳转失败:', err);
          },
        });
      }

      if (val == '增值优惠') {
        return uni.navigateTo({
          url: '/pages/home/<USER>',
          success: () => {
            console.log('跳转成功');
          },
          fail: err => {
            console.error('跳转失败:', err);
          },
        });
      }

      if (val == '客服') {
        return uni.navigateTo({ url: '/pages-home/online-service' });
      }

      if (val == '促销活动') {
        return uni.navigateTo({ url: '/pages/home/<USER>' });
      }

      if (val == '更多培训') {
        return uni.navigateTo({ url: '/pages-large/training-list' });
      }
      if (val == '家政人广场') {
        return uni.navigateTo({ url: '/pages/demand-square/index' });
      }
    },

    // 跳转到公告列表页面
    goToAnnouncementList() {
      console.log('跳转到公告列表页面');
      uni.navigateTo({
        url: '/pages/announcement/announcement-list',
        fail: (err) => {
          console.error('跳转公告列表页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 跳转到最新公告详情页面
    async goToLatestAnnouncement() {
      try {
        console.log('获取最新公告并跳转');
        const result = await getLatestAnnouncement();
        console.log('最新公告数据:', result);

        if (result && result.id) {
          uni.navigateTo({
            url: `/pages/announcement/announcement-detail?id=${result.id}`,
            fail: (err) => {
              console.error('跳转公告详情页面失败:', err);
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '暂无公告内容',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取最新公告失败:', error);
        uni.showToast({
          title: '获取公告失败',
          icon: 'none'
        });
      }



      // 轮播图链接处理
      if (val && val.startsWith('/pages/home/')) {
        return uni.navigateTo({
          url: val,
          fail: () => {
            uni.showToast({
              title: '页面正在建设中...',
              icon: 'none',
            });
          }
        });
      }

      // 提示正在建设中...
      uni.showToast({
        title: '正在建设中...',
        icon: 'none',
      });
    },
    // 菜单点击
    handleNavClick(data) {
      console.log(data);
    },

    handleCancel() {
      console.log('取消');
    },

    // 切换余额显示/隐藏
    toggleBalanceVisibility() {
      this.showBalance = !this.showBalance;
    },

    // 生成小程序码（手动生成，用于重新生成）
    async generateQRCode() {
      console.log('=== 开始手动生成小程序码 ===');

      // 显示加载提示
      uni.showLoading({
        title: '正在获取门店码...',
        mask: true
      });

      // 先尝试从后端获取
      await this.loadStoreMiniQrCode();

      // 如果后端获取成功，就不需要手动生成了
      if (this.qrcodeUrl) {
        console.log('后端已有小程序码，无需手动生成');
        return;
      }

      console.log('后端没有小程序码，开始手动生成...');
      console.log('当前用户信息:', this.user);

      // 更新加载提示文本
      uni.showLoading({
        title: '正在生成门店码...',
        mask: true
      });

      this.generating = true;

      try {
        // 调用自定义小程序二维码生成API
        const requestData = {
          app_id: 'wxc33730c9e09594f8',        // 替换为您的小程序AppID
          app_secret: '7f5d720149219178cf28537e33d48202', // 替换为您的小程序AppSecret
          scene: 'storeuuid/' + (this.user?.store_uuid || ''),  // 场景值，可以包含门店信息
          page: 'pages/homePage/stylePageB',                  // 小程序页面路径
          width: 430,                                 // 二维码宽度
          auto_color: true,                          // 自动配置线条颜色
          is_hyaline: false                          // 是否需要透明底色
        };

        console.log('请求参数:', requestData);
        console.log('开始调用 generateCustomMiniQr API...');

        const result = await generateCustomMiniQr(requestData);

        console.log('API 返回结果:', result);

        if (result && result.qr_url) {
          this.qrcodeUrl = result.qr_url;
          console.log('二维码URL设置成功:', this.qrcodeUrl);
          uni.showToast({
            title: '小程序码生成成功',
            icon: 'success'
          });
        } else {
          console.error('API返回结果无效:', result);
          throw new Error('生成失败，请检查小程序配置');
        }
      } catch (error) {
        console.error('=== 生成小程序码失败 ===');
        console.error('错误详情:', error);
        console.error('错误堆栈:', error.stack);
        uni.showToast({
          title: '生成失败：' + (error.message || '请稍后重试'),
          icon: 'none'
        });
      } finally {
        this.generating = false;
        uni.hideLoading();
        console.log('=== 生成小程序码结束 ===');
      }
    },



    // 分享小程序码
    async shareQRCode() {
      if (!this.qrcodeUrl) {
        uni.showToast({
          title: '请先生成小程序码',
          icon: 'none'
        });
        return;
      }

      console.log('=== 开始保存二维码 ===');
      console.log('二维码URL:', this.qrcodeUrl);

      try {
        // 先检查相册权限
        const authResult = await this.checkPhotoAlbumAuth();
        if (!authResult) {
          return;
        }

        // 显示加载提示
        uni.showLoading({
          title: '保存中...'
        });

        // 如果是网络图片，先下载到本地
        let localPath = this.qrcodeUrl;
        if (this.qrcodeUrl.startsWith('http')) {
          console.log('检测到网络图片，开始下载...');
          localPath = await this.downloadImage(this.qrcodeUrl);
          console.log('下载完成，本地路径:', localPath);
        }

        // 保存图片到相册
        await this.saveImageToAlbum(localPath);

        uni.hideLoading();
        uni.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
        console.log('=== 保存二维码成功 ===');

      } catch (error) {
        uni.hideLoading();
        console.error('=== 保存二维码失败 ===');
        console.error('错误详情:', error);
        uni.showToast({
          title: '保存失败：' + (error.message || '请稍后重试'),
          icon: 'none'
        });
      }
    },

    // 检查相册权限
    checkPhotoAlbumAuth() {
      return new Promise((resolve) => {
        uni.getSetting({
          success: (res) => {
            console.log('当前权限设置:', res.authSetting);
            if (res.authSetting['scope.writePhotosAlbum'] === false) {
              // 用户之前拒绝了权限，引导用户手动开启
              uni.showModal({
                title: '需要相册权限',
                content: '需要访问您的相册来保存二维码，请在设置中开启相册权限',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting();
                  }
                  resolve(false);
                }
              });
            } else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
              // 用户还没有授权过，请求授权
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: () => {
                  console.log('相册权限授权成功');
                  resolve(true);
                },
                fail: () => {
                  console.log('相册权限授权失败');
                  uni.showToast({
                    title: '需要相册权限才能保存图片',
                    icon: 'none'
                  });
                  resolve(false);
                }
              });
            } else {
              // 用户已经授权
              console.log('已有相册权限');
              resolve(true);
            }
          },
          fail: () => {
            console.log('获取权限设置失败');
            resolve(true); // 获取失败时继续尝试保存
          }
        });
      });
    },

    // 下载网络图片到本地
    downloadImage(url) {
      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: url,
          success: (res) => {
            if (res.statusCode === 200) {
              console.log('图片下载成功:', res.tempFilePath);
              resolve(res.tempFilePath);
            } else {
              reject(new Error('图片下载失败'));
            }
          },
          fail: (err) => {
            console.error('图片下载失败:', err);
            reject(new Error('图片下载失败'));
          }
        });
      });
    },

    // 保存图片到相册
    saveImageToAlbum(filePath) {
      return new Promise((resolve, reject) => {
        uni.saveImageToPhotosAlbum({
          filePath: filePath,
          success: () => {
            console.log('图片保存成功');
            resolve();
          },
          fail: (err) => {
            console.error('图片保存失败:', err);
            reject(new Error('保存到相册失败'));
          }
        });
      });
    },

    // 跳转到培训详情页面
    goTrainingDetail(item) {
      console.log('点击培训项目:', item);
      console.log('培训项目标题:', item.title);
      console.log('培训项目视频URL:', item.videoUrl);

      if (!item.title) {
        console.error('培训项目数据不完整:', item);
        uni.showToast({
          title: '课程信息不完整',
          icon: 'none'
        });
        return;
      }

      // 构建跳转参数，包含更多课程信息
      const params = {
        title: encodeURIComponent(item.title),
        uuid: item.uuid || '',
        views: item.views || 0,
        description: encodeURIComponent(item.description || ''),
        content_type: item.content_type || 1,
        duration: item.duration || 0
      };

      // 如果有视频URL，添加到参数中
      if (item.videoUrl) {
        params.videoUrl = encodeURIComponent(item.videoUrl);
      }

      // 构建URL参数字符串
      const paramString = Object.keys(params)
        .map(key => `${key}=${params[key]}`)
        .join('&');

      const url = `/pages-large/training-detail?${paramString}`;
      console.log('跳转URL:', url);

      uni.navigateTo({
        url: url,
        success: () => {
          console.log('跳转成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 切换到员工端
    switchToStaffMode() {
      console.log('切换到员工端');

      uni.showModal({
        title: '切换到员工端',
        content: '确定要切换到员工端吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.handleStaffModeSwitch();
          }
        }
      });
    },

    // 处理员工端切换逻辑
    async handleStaffModeSwitch() {
      try {
        // 1. 检查本地缓存是否有staff账号信息
        const cachedStaffInfo = uni.getStorageSync('staffInfo');
        const cachedStaffToken = uni.getStorageSync('staffToken');

        console.log('检查本地缓存:', {
          hasStaffInfo: !!cachedStaffInfo,
          hasStaffToken: !!cachedStaffToken
        });

        if (cachedStaffInfo && cachedStaffToken) {
          // 有缓存，直接跳转
          console.log('发现本地员工缓存，直接跳转');
          this.jumpToStaffPage();
          return;
        }

        // 2. 没有缓存，需要调用接口验证
        console.log('本地无员工缓存，开始接口验证');

        // 获取当前用户手机号 - 直接从本地存储获取
        let mobile = null;

        // 尝试多种可能的存储方式获取手机号
        const userInfo = uni.getStorageSync('userInfo');
        const user = uni.getStorageSync('user');
        const directMobile = uni.getStorageSync('mobile');

        if (userInfo && userInfo.mobile) {
          mobile = userInfo.mobile;
        } else if (user && user.mobile) {
          mobile = user.mobile;
        } else if (directMobile) {
          mobile = directMobile;
        } else if (this.$store.state.userInfo && this.$store.state.userInfo.mobile) {
          mobile = this.$store.state.userInfo.mobile;
        } else if (this.$store.state.user && this.$store.state.user.mobile) {
          mobile = this.$store.state.user.mobile;
        }

        console.log('获取到的手机号:', mobile);

        if (!mobile) {
          uni.showToast({
            title: '无法获取用户手机号',
            icon: 'none'
          });
          return;
        }

        uni.showLoading({
          title: '验证中...',
          mask: true
        });

        // 3. 检查手机号下是否有员工账号
        const staffAuthApi = require('@/api/staff-auth.js');
        const checkResult = await staffAuthApi.checkStaffAccount({ mobile: mobile });

        if (!checkResult.hasStaffAccount) {
          // 没有员工账号
          uni.hideLoading();
          uni.showToast({
            title: '暂无服务人员账号',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        // 4. 有员工账号，获取默认员工账号token
        console.log('发现员工账号，开始切换');
        const switchResult = await staffAuthApi.switchToStaffAccount({ mobile: mobile });

        uni.hideLoading();

        if (switchResult.access_token) {
          // 切换成功，跳转到员工端
          console.log('员工账号切换成功');
          this.jumpToStaffPage();
        } else {
          uni.showToast({
            title: '切换失败，请重试',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('切换到员工端失败:', error);
        uni.hideLoading();

        // 根据错误类型显示不同提示
        let errorMessage = '切换失败，请重试';
        if (error.message && error.message.includes('暂无服务人员账号')) {
          errorMessage = '暂无服务人员账号';
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 跳转到员工端页面
    jumpToStaffPage() {
      // 导入缓存清理工具
      const { cleanupAfterRoleSwitch } = require('@/utlis/auth.js');

      // 执行角色切换后的缓存清理
      cleanupAfterRoleSwitch('staff');

      console.log('角色已设置为员工端，管理端缓存已清理');

      // 跳转到员工端首页
      uni.reLaunch({
        url: '/pages-staff/home/<USER>'
      });

      uni.showToast({
        title: '已切换到员工端',
        icon: 'success'
      });
    },
  },
};
</script>

<style lang="scss" scoped>
// 现代化仪表板样式
.modern-dashboard {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 头部区域
.dashboard-header {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;
  // 添加状态栏安全区域，增加更多顶部空间避免遮挡时间
  padding-top: calc(var(--status-bar-height) + 80rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }

  .top-nav {
    margin-bottom: 40rpx;
    padding-top: 0;

    .welcome-section {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 12rpx;
      width: 100%;

      .welcome-text {
        display: block;
        color: rgba(255, 255, 255, 0.9);
        font-size: 28rpx;
        margin-bottom: 8rpx;
      }

      .store-info-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 16rpx;
      }

      .store-name-container {
        display: flex;
        align-items: center;
        gap: 8rpx;
        flex-shrink: 0;
      }

      .store-name {
        color: #fff;
        font-size: 36rpx;
        font-weight: bold;
      }

      .store-switch-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 20rpx;
        padding: 8rpx 16rpx;
        display: flex;
        align-items: center;
        gap: 6rpx;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(0.95);
        }

        .switch-text {
          font-size: 22rpx;
          color: #fff;
          font-weight: 500;
        }
      }

    // 角色切换按钮容器
    .role-switch-container {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 16rpx;
      padding: 0 4rpx;
    }

    .role-switch-btn {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
      backdrop-filter: blur(10rpx);
      border: none;
      border-radius: 24rpx;
      padding: 10rpx 20rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1),
                  inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:active {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%);
        transform: scale(0.96);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15),
                    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);

        &::before {
          left: 100%;
        }
      }

      .switch-text {
        font-size: 24rpx;
        color: #fff;
        font-weight: 600;
        text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        letter-spacing: 0.5rpx;
      }
    }
    }
  }

  .quick-status {
    display: flex;
    gap: 20rpx;

    .status-card {
      flex: 1;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10rpx);
      border-radius: 20rpx;
      padding: 24rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      border: 1rpx solid rgba(255, 255, 255, 0.3);

      &.user-info-card {
        flex: 1;
        padding: 20rpx;

        .user-avatar-container {
          position: relative;

          .user-avatar {
            width: 88rpx;
            height: 88rpx;
            border-radius: 44rpx;
            overflow: hidden;
            border: 3rpx solid rgba(253, 209, 24, 0.3);

            image {
              width: 100%;
              height: 100%;
            }
          }

          .online-status {
            position: absolute;
            bottom: 2rpx;
            right: 2rpx;
            width: 16rpx;
            height: 16rpx;
            background: #09be89;
            border-radius: 8rpx;
            border: 2rpx solid #fff;
          }
        }

        .user-info {
          flex: 1;

          .greeting-time {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 6rpx;
            font-weight: 400;
          }

          .user-name-section {
            display: flex;
            align-items: center;
            gap: 8rpx;

            .user-name {
              font-size: 28rpx;
              font-weight: bold;
              color: #333;
            }

            .user-role {
              background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
              color: #fff;
              font-size: 20rpx;
              padding: 2rpx 8rpx;
              border-radius: 8rpx;
              font-weight: 500;
            }
          }
        }

        .notification-icon {
          position: relative;
          padding: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .notification-badge {
            position: absolute;
            top: 4rpx;
            right: 4rpx;
            width: 12rpx;
            height: 12rpx;
            background: #ff403f;
            border-radius: 6rpx;
            border: 2rpx solid #fff;
          }
        }
      }

      &.qrcode-card {
        .qrcode-icon-bg {
          width: 48rpx;
          height: 48rpx;
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
          border-radius: 24rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .qrcode-icon {
            font-size: 24rpx;
            color: #fff;
            font-weight: bold;
          }
        }
      }

      .status-icon {
        width: 48rpx;
        height: 48rpx;
        background: rgba(253, 209, 24, 0.1);
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .status-info {
        flex: 1;

        .status-label {
          display: block;
          font-size: 24rpx;
          color: #666;
          margin-bottom: 4rpx;
        }

        .status-value {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }
      }

      .status-toggle {
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

  }

  // 余额卡片区域
  .balance-section {
    margin-top: 20rpx;

    .balance-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10rpx);
      border-radius: 20rpx;
      padding: 24rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      border: 1rpx solid rgba(255, 255, 255, 0.3);

      .balance-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .balance-title-section {
          flex: 1;

          .balance-title {
            display: block;
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 4rpx;
          }

          .balance-subtitle {
            display: block;
            font-size: 24rpx;
            color: #666;
          }
        }

        .balance-toggle {
          width: 40rpx;
          height: 40rpx;
          background: rgba(0, 0, 0, 0.05);
          border-radius: 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .balance-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .balance-amount {
          display: flex;
          align-items: baseline;
          gap: 4rpx;

          .currency-symbol {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
          }

          .amount-value {
            font-size: 48rpx;
            font-weight: bold;
            color: #333;
            letter-spacing: 1rpx;
          }
        }

        .balance-actions {
          display: flex;
          gap: 12rpx;

          .action-btn {
            padding: 12rpx 20rpx;
            border-radius: 20rpx;
            display: flex;
            align-items: center;
            gap: 6rpx;
            font-size: 24rpx;
            font-weight: 500;

            &.recharge-btn {
              background: linear-gradient(135deg, #09be89 0%, #00a870 100%);
              color: #fff;
            }

            &.withdraw-btn {
              background: linear-gradient(135deg, #ff801b 0%, #ff6b00 100%);
              color: #fff;
            }
          }
        }
      }
    }
  }

      // 版本过期提醒 - 小巧右对齐样式
      .version-expire-notice {
        flex-shrink: 0;

        .expire-notice-content {
          background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(8rpx);
          border-radius: 12rpx;
          padding: 6rpx 12rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;
          border: 1rpx solid rgba(255, 255, 255, 0.2);
          width: fit-content;
          min-width: auto;

          .expire-dot {
            width: 8rpx;
            height: 8rpx;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4rpx;
            flex-shrink: 0;
            animation: pulse-glow 2s infinite;
          }

          .expire-text {
            font-size: 20rpx;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            white-space: nowrap;
          }
        }
      }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

// 仪表板内容区域
.dashboard-content {
  padding: 0;
}

// 数据概览区域
.data-overview {
  margin: 20rpx 30rpx 40rpx;

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .time-filter {
      display: flex;
      background: #f5f5f5;
      border-radius: 20rpx;
      padding: 4rpx;

      .filter-tab {
        padding: 12rpx 24rpx;
        font-size: 26rpx;
        color: #666;
        border-radius: 16rpx;
        transition: all 0.3s ease;

        &.active {
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
          color: #fff;
          font-weight: 500;
        }
      }
    }
  }

  .overview-cards {
    display: flex;
    gap: 20rpx;

    .overview-card {
      flex: 1;
      background: #fff;
      border-radius: 20rpx;
      padding: 24rpx;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
      border: 1rpx solid rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4rpx;
        background: linear-gradient(90deg, #fdd118 0%, #ff801b 100%);
      }

      .card-icon {
        width: 56rpx;
        height: 56rpx;
        border-radius: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16rpx;

        &.staff-icon {
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        }

        &.customer-icon {
          background: linear-gradient(135deg, #09be89 0%, #00a862 100%);
        }

        &.order-icon {
          background: linear-gradient(135deg, #ff801b 0%, #ff403f 100%);
        }
      }

      .card-content {
        .card-number {
          display: block;
          font-size: 36rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }

        .card-label {
          display: block;
          font-size: 24rpx;
          color: #666;
        }
      }

      .card-trend {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        display: flex;
        align-items: center;
        gap: 4rpx;
        font-size: 20rpx;
        font-weight: 500;

        &.up {
          color: #09be89;
        }

        &.down {
          color: #ff403f;
        }

        &.stable {
          color: #999;
        }
      }
    }
  }
}

// 通知滚动播报
.notification-announcements {
  margin: 20rpx 30rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }

  .notification-header {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 16rpx;

    .notification-icon {
      width: 32rpx;
      height: 32rpx;
      background: rgba(253, 209, 24, 0.1);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .notification-label {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      flex: 1;
    }

    .more-link {
      display: flex;
      align-items: center;
      gap: 4rpx;
      font-size: 24rpx;
      color: #666;

      .arrow {
        font-size: 20rpx;
        color: #999;
      }
    }
  }

  .notification-content {
    height: 60rpx;
    overflow: hidden;
    position: relative;

    .notification-scroll {
      animation: scrollUp 15s linear infinite;

      .notification-item {
        height: 60rpx;
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 0 16rpx;

        .notification-title {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }

        .notification-desc {
          font-size: 24rpx;
          color: #666;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .time-info {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
}

@keyframes scrollUp {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-60rpx);
  }
  40% {
    transform: translateY(-120rpx);
  }
  60% {
    transform: translateY(-180rpx);
  }
  80% {
    transform: translateY(-240rpx);
  }
  100% {
    transform: translateY(-300rpx);
  }
}

// 核心功能区域
.core-functions {
  margin: 20rpx 30rpx 40rpx;

  .section-header {
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .function-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16rpx;

    .function-card {
      background: #fff;
      border-radius: 16rpx;
      padding: 24rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
      border: 1rpx solid rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
      }

      .function-icon {
        width: 48rpx;
        height: 48rpx;
        background: rgba(253, 209, 24, 0.1);
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .function-info {
        flex: 1;
        min-width: 0;

        .function-title {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 4rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .function-desc {
          display: block;
          font-size: 22rpx;
          color: #666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .function-arrow {
        width: 24rpx;
        height: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }
    }

    // 宽按钮样式 - 占据两列宽度
    .function-card-wide {
      grid-column: 1 / -1;
      justify-content: center;

      .function-info {
        text-align: center;

        .function-title {
          text-align: center;
        }

        .function-desc {
          text-align: center;
        }
      }
    }
  }
}

// 轮播图区域
.banner-section {
  margin: 20rpx 30rpx;

  .banner-swiper {
    height: 320rpx;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

    .banner-item {
      position: relative;
      width: 100%;
      height: 100%;

      .banner-image {
        width: 100%;
        height: 100%;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
        padding: 40rpx 30rpx 30rpx;

        .banner-content {
          .banner-title {
            display: block;
            color: #fff;
            font-size: 32rpx;
            font-weight: bold;
            margin-bottom: 8rpx;
            text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
          }

          .banner-subtitle {
            display: block;
            color: rgba(255, 255, 255, 0.9);
            font-size: 24rpx;
            text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
}

// 培训区域样式
.training-section {
  margin: 20rpx 30rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .more-link {
      font-size: 26rpx;
      color: #999;
      display: flex;
      align-items: center;
      gap: 4rpx;

      .arrow {
        font-size: 24rpx;
      }
    }
  }

  .training-list {
    .training-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .training-info {
        flex: 1;
        margin-right: 20rpx;

        .training-title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
          line-height: 1.4;
          font-weight: 500;
        }

        .training-stats {
          font-size: 24rpx;
          color: #999;
        }
      }

      .training-image {
        width: 120rpx;
        height: 80rpx;
        border-radius: 12rpx;
        overflow: hidden;
      }
    }
  }
}

// 门店选择弹窗样式
.store-selector-modal {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20rpx;
      background: #f5f5f5;
    }
  }

  .modal-content {
    padding: 20rpx 0;

    .store-list {
      .store-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        transition: background-color 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &.active {
          background-color: rgba(253, 209, 24, 0.1);
        }

        .store-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .check-icon {
          width: 24rpx;
          height: 24rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

// 小程序码弹窗样式
.qrcode-modal {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20rpx;
      background: #f5f5f5;
    }
  }

  .modal-content {
    padding: 30rpx;
    text-align: center;

    .modal-subtitle {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 30rpx;
    }

    .qrcode-display {
      margin-bottom: 30rpx;

      .qrcode-preview {
        .qrcode-image {
          width: 600rpx;
          height: 600rpx;
          border-radius: 20rpx;
          border: 2rpx solid #f0f0f0;
          margin-bottom: 20rpx;
        }

        .qrcode-tips {
          font-size: 24rpx;
          color: #999;
        }
      }

      .qrcode-placeholder {
        .placeholder-icon {
          width: 200rpx;
          height: 200rpx;
          background: #f8f8f8;
          border: 2rpx dashed #ddd;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16rpx;
        }

        .placeholder-text {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .modal-actions {
      display: flex;
      gap: 16rpx;
      justify-content: center;

      .action-btn {
        padding: 16rpx 32rpx;
        border-radius: 20rpx;
        font-size: 26rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8rpx;
        transition: all 0.3s ease;

        &.primary {
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
          color: #fff;
          border: none;

          &.loading {
            opacity: 0.7;
          }
        }

        &.secondary {
          background: #fff;
          color: #fdd118;
          border: 2rpx solid #fdd118;
        }
      }
    }
  }
}

// 响应式和动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.overview-card,
.function-card,
.qrcode-card,
.access-item {
  animation: fadeInUp 0.6s ease-out;
}

.overview-card:nth-child(1) { animation-delay: 0.1s; }
.overview-card:nth-child(2) { animation-delay: 0.2s; }
.overview-card:nth-child(3) { animation-delay: 0.3s; }

.function-card:nth-child(1) { animation-delay: 0.1s; }
.function-card:nth-child(2) { animation-delay: 0.2s; }
.function-card:nth-child(3) { animation-delay: 0.3s; }
.function-card:nth-child(4) { animation-delay: 0.4s; }

// 通用样式
.section-header {
  .section-title {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -8rpx;
      left: 0;
      width: 40rpx;
      height: 4rpx;
      background: linear-gradient(90deg, #fdd118 0%, #ff801b 100%);
      border-radius: 2rpx;
    }
  }
}

/* 门店选择弹窗样式 */
.store-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.store-modal {
  background: #fff;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  margin: 0 auto;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      width: 50rpx;
      height: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: #f5f5f5;

      .close-icon {
        font-size: 32rpx;
        color: #999;
        line-height: 1;
      }
    }
  }

  .modal-content {
    padding: 20rpx 0;

    .store-list {
      .store-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 30rpx;
        border-bottom: 1rpx solid #f8f8f8;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background: #f8f8f8;
        }

        &.active {
          background: rgba(253, 209, 24, 0.1);
        }

        .store-info {
          flex: 1;

          .store-main {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;

            .store-name {
              font-size: 30rpx;
              color: #333;
              font-weight: 500;
              margin-right: 12rpx;
            }

            .current-badge {
              background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
              color: #fff;
              font-size: 20rpx;
              padding: 4rpx 8rpx;
              border-radius: 8rpx;

              .badge-text {
                font-size: 20rpx;
                font-weight: 500;
              }
            }
          }

          .store-details {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .store-id {
              font-size: 24rpx;
              color: #999;
            }

            .store-status {
              display: flex;
              align-items: center;
              gap: 6rpx;

              .status-dot {
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background: #ccc;
              }

              &.active .status-dot {
                background: #09be89;
              }

              .status-text {
                font-size: 22rpx;
                color: #666;
              }

              &.active .status-text {
                color: #09be89;
              }
            }
          }
        }

        .store-action {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40rpx;
          height: 40rpx;

          .check-icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .switch-icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

/* 小程序码弹窗样式 */
.qrcode-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.qrcode-modal {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 95%;
  max-width: 750rpx;
  margin: 0 auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.modal-content {
  text-align: center;
}

.modal-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  display: block;
}

.qrcode-display {
  margin: 40rpx 0;
  min-height: 700rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qrcode-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-image {
  width: 600rpx;
  height: 600rpx;
  border-radius: 20rpx;
  border: 2rpx solid #f0f0f0;
  display: block;
}

.qrcode-tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}

.qrcode-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
}

.placeholder-icon {
  margin-bottom: 20rpx;
}

.placeholder-qr {
  font-size: 80rpx;
  color: #ddd;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
}



.modal-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  margin-top: 40rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #fdd118 0%, #f0c419 100%);
  color: #333;
}

.action-btn.secondary {
  background: #f8f8f8;
  color: #666;
  border: 2rpx solid #e0e0e0;
}

.action-btn.loading {
  opacity: 0.7;
  pointer-events: none;
}
</style>

