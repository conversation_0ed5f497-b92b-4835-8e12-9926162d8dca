<template>
  <view class="announcement-list-page">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <appHead left fixed title="通知公告"></appHead>
    </view>

    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-box">
        <u-icon name="search" color="#999" size="32"></u-icon>
        <input
          class="search-input"
          placeholder="搜索公告标题或内容"
          v-model="searchKeyword"
          @confirm="onSearch"
          confirm-type="search"
        />
        <view class="search-btn" @click="onSearch" v-if="searchKeyword">
          <text>搜索</text>
        </view>
      </view>
    </view>

    <!-- 公告列表 -->
    <scroll-view
      class="announcement-scroll"
      scroll-y="true"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
      :lower-threshold="100"
    >
      <view class="announcement-list">
        <view 
          class="announcement-item" 
          v-for="(item, index) in announcementList" 
          :key="index"
          @click="goToAnnouncementDetail(item)"
        >
          <view class="announcement-header">
            <view class="announcement-title">{{ item.title }}</view>
            <view class="announcement-time">{{ item.create_time }}</view>
          </view>
          <view class="announcement-content">{{ item.content }}</view>
          <view class="announcement-footer">
            <view class="announcement-type">{{ getTypeText(item.type) }}</view>
            <u-icon name="arrow-right" color="#ccc" size="24"></u-icon>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <u-loading-icon mode="flower"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more-container" v-if="!hasMore && announcementList.length > 0">
        <text class="no-more-text">没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-if="!loading && announcementList.length === 0">
        <u-icon name="file-text" color="#ccc" size="120"></u-icon>
        <text class="empty-text">暂无公告</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getAnnouncementList } from '@/api/announcement.js';

export default {
  data() {
    return {
      searchKeyword: '',
      announcementList: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
  },
  onLoad() {
    this.loadAnnouncementList(true);
  },
  methods: {
    // 加载公告列表
    async loadAnnouncementList(reset = false) {
      if (this.loading) return;
      
      try {
        this.loading = true;
        
        if (reset) {
          this.currentPage = 1;
          this.announcementList = [];
          this.hasMore = true;
        }

        const params = {
          page: this.currentPage,
          size: this.pageSize
        };

        if (this.searchKeyword) {
          params.keyword = this.searchKeyword;
        }

        console.log('加载公告列表，参数:', params);
        const result = await getAnnouncementList(params);
        console.log('公告列表API返回数据:', result);

        if (result && result.list && result.list.length > 0) {
          if (reset) {
            this.announcementList = result.list;
          } else {
            this.announcementList = [...this.announcementList, ...result.list];
          }
          
          this.total = result.total || 0;
          this.hasMore = this.announcementList.length < this.total;
          this.currentPage++;
        } else {
          if (reset) {
            this.announcementList = [];
          }
          this.hasMore = false;
        }
      } catch (error) {
        console.error('加载公告列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 搜索
    onSearch() {
      console.log('搜索公告:', this.searchKeyword);
      this.loadAnnouncementList(true);
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.loadAnnouncementList(true);
    },

    // 上拉加载更多
    onLoadMore() {
      if (!this.loading && this.hasMore) {
        this.loadAnnouncementList(false);
      }
    },

    // 跳转到公告详情
    goToAnnouncementDetail(item) {
      console.log('跳转到公告详情:', item);
      uni.navigateTo({
        url: `/pages/announcement/announcement-detail?id=${item.id}`,
        fail: (err) => {
          console.error('跳转公告详情页面失败:', err);
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        '1': '系统公告',
        '2': '活动通知',
        '3': '功能更新',
        '4': '维护通知'
      };
      return typeMap[type] || '通知';
    }
  }
};
</script>

<style lang="scss" scoped>
.announcement-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.nav-header {
  background-color: #ffffff;
}

.search-container {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f5f5f5;

  .search-box {
    display: flex;
    align-items: center;
    background-color: #f8f8f8;
    border-radius: 24rpx;
    padding: 16rpx 20rpx;
    gap: 12rpx;

    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .search-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 24rpx;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
    }
  }
}

.announcement-scroll {
  height: calc(100vh - 160rpx);
}

.announcement-list {
  padding: 20rpx 30rpx;

  .announcement-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    }

    .announcement-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12rpx;

      .announcement-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        flex: 1;
        margin-right: 20rpx;
        line-height: 1.4;
      }

      .announcement-time {
        font-size: 24rpx;
        color: #999;
        white-space: nowrap;
      }
    }

    .announcement-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 16rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    .announcement-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .announcement-type {
        background: rgba(253, 209, 24, 0.1);
        color: #ff801b;
        font-size: 22rpx;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.no-more-container {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;

  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}
</style>
