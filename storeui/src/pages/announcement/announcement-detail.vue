<template>
  <view class="announcement-detail-page">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <appHead left fixed title="公告详情"></appHead>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <u-loading-icon mode="flower"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 公告详情 -->
    <view class="announcement-detail" v-else-if="announcementDetail">
      <view class="detail-header">
        <view class="detail-title">{{ announcementDetail.title }}</view>
        <view class="detail-meta">
          <view class="detail-type">{{ getTypeText(announcementDetail.type) }}</view>
          <view class="detail-time">{{ announcementDetail.create_time }}</view>
        </view>
      </view>

      <view class="detail-content">
        <rich-text :nodes="formatContent(announcementDetail.content)" @tap="handleRichTextTap"></rich-text>
      </view>

      <view class="detail-footer">
        <view class="update-info" v-if="announcementDetail.update_time && announcementDetail.update_time !== announcementDetail.create_time">
          更新时间：{{ announcementDetail.update_time }}
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container" v-else>
      <u-icon name="info-circle" color="#ccc" size="120"></u-icon>
      <text class="error-text">公告不存在或已删除</text>
      <view class="error-actions">
        <view class="back-btn" @click="goBack">
          返回列表
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAnnouncementDetail } from '@/api/announcement.js';

export default {
  data() {
    return {
      announcementId: null,
      announcementDetail: null,
      loading: true
    };
  },
  onLoad(options) {
    if (options.id) {
      this.announcementId = options.id;
      this.loadAnnouncementDetail();
    } else {
      this.loading = false;
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
    }
  },
  methods: {
    // 加载公告详情
    async loadAnnouncementDetail() {
      try {
        this.loading = true;
        console.log('加载公告详情，ID:', this.announcementId);
        
        const result = await getAnnouncementDetail(this.announcementId);
        console.log('公告详情API返回数据:', result);
        
        if (result) {
          this.announcementDetail = result;
        } else {
          this.announcementDetail = null;
          uni.showToast({
            title: '公告不存在',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载公告详情失败:', error);
        this.announcementDetail = null;
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        '1': '系统公告',
        '2': '活动通知',
        '3': '功能更新',
        '4': '维护通知'
      };
      return typeMap[type] || '通知';
    },

    // 格式化内容
    formatContent(content) {
      if (!content) return '';

      // 将换行符转换为<br>标签
      let formattedContent = content.replace(/\n/g, '<br/>');

      // 处理自定义小程序链接 - 方案一：简单链接格式
      formattedContent = formattedContent.replace(
        /<a\s+href="miniapp:\/\/([^"]+)"[^>]*>([^<]+)<\/a>/g,
        '<span class="miniapp-link" data-url="$1" style="color: #007aff; text-decoration: underline;">$2</span>'
      );

      // 处理JSON配置链接 - 方案二：复杂配置格式
      // 格式：{{link:{"url":"pages/order/order-list","text":"查看订单","params":{"status":"pending"}}}}
      formattedContent = formattedContent.replace(
        /\{\{link:(\{[^}]+\})\}\}/g,
        (match, jsonStr) => {
          try {
            const linkConfig = JSON.parse(jsonStr);
            const url = linkConfig.url || '';
            const text = linkConfig.text || '点击跳转';
            const params = linkConfig.params || {};

            // 构建完整URL
            let fullUrl = url;
            if (Object.keys(params).length > 0) {
              const paramStr = Object.keys(params)
                .map(key => `${key}=${encodeURIComponent(params[key])}`)
                .join('&');
              fullUrl += (url.includes('?') ? '&' : '?') + paramStr;
            }

            return `<span class="miniapp-link" data-url="${fullUrl}" style="color: #007aff; text-decoration: underline;">${text}</span>`;
          } catch (e) {
            console.error('解析链接配置失败:', e);
            return match; // 返回原始文本
          }
        }
      );

      return formattedContent;
    },

    // 处理富文本点击事件
    handleRichTextTap(e) {
      console.log('富文本点击事件:', e);

      // 获取点击的元素
      const detail = e.detail || {};
      const target = detail.target || {};

      // 检查是否点击了小程序链接
      if (target.dataset && target.dataset.url) {
        const url = target.dataset.url;
        console.log('点击小程序链接:', url);
        this.navigateToMiniappPage(url);
      }
    },

    // 跳转到小程序页面
    navigateToMiniappPage(url) {
      try {
        // 解析URL，支持带参数的页面
        let targetUrl = url;

        // 确保URL以/开头
        if (!targetUrl.startsWith('/')) {
          targetUrl = '/' + targetUrl;
        }

        console.log('跳转到页面:', targetUrl);

        uni.navigateTo({
          url: targetUrl,
          success: () => {
            console.log('页面跳转成功:', targetUrl);
          },
          fail: (err) => {
            console.error('页面跳转失败:', err);

            // 如果navigateTo失败，尝试使用switchTab（可能是tabBar页面）
            uni.switchTab({
              url: targetUrl,
              success: () => {
                console.log('Tab页面跳转成功:', targetUrl);
              },
              fail: (tabErr) => {
                console.error('Tab页面跳转也失败:', tabErr);
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('解析跳转URL失败:', error);
        uni.showToast({
          title: '链接格式错误',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          // 如果无法返回，则跳转到公告列表
          uni.redirectTo({
            url: '/pages/announcement/announcement-list'
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.announcement-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.nav-header {
  background-color: #ffffff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.announcement-detail {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .detail-header {
    padding: 30rpx;
    border-bottom: 1px solid #f5f5f5;

    .detail-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }

    .detail-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .detail-type {
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        color: #fff;
        font-size: 22rpx;
        padding: 6rpx 16rpx;
        border-radius: 16rpx;
      }

      .detail-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .detail-content {
    padding: 30rpx;
    font-size: 30rpx;
    color: #333;
    line-height: 1.6;

    // rich-text 样式
    :deep(rich-text) {
      word-break: break-all;
    }

    // 小程序链接样式
    :deep(.miniapp-link) {
      color: #007aff;
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .detail-footer {
    padding: 20rpx 30rpx;
    border-top: 1px solid #f5f5f5;
    background-color: #fafafa;

    .update-info {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .error-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }

  .error-actions {
    margin-top: 40rpx;

    .back-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 28rpx;
      padding: 16rpx 32rpx;
      border-radius: 24rpx;
      text-align: center;
    }
  }
}
</style>
