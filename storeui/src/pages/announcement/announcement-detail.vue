<template>
  <view class="announcement-detail-page">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <appHead left fixed title="公告详情"></appHead>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <u-loading-icon mode="flower"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 公告详情 -->
    <view class="announcement-detail" v-else-if="announcementDetail">
      <view class="detail-header">
        <view class="detail-title">{{ announcementDetail.title }}</view>
        <view class="detail-meta">
          <view class="detail-type">{{ getTypeText(announcementDetail.type) }}</view>
          <view class="detail-time">{{ announcementDetail.create_time }}</view>
        </view>
      </view>

      <view class="detail-content">
        <rich-text :nodes="formatContent(announcementDetail.content)"></rich-text>
      </view>

      <view class="detail-footer">
        <view class="update-info" v-if="announcementDetail.update_time && announcementDetail.update_time !== announcementDetail.create_time">
          更新时间：{{ announcementDetail.update_time }}
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container" v-else>
      <u-icon name="info-circle" color="#ccc" size="120"></u-icon>
      <text class="error-text">公告不存在或已删除</text>
      <view class="error-actions">
        <view class="back-btn" @click="goBack">
          返回列表
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAnnouncementDetail } from '@/api/announcement.js';

export default {
  data() {
    return {
      announcementId: null,
      announcementDetail: null,
      loading: true
    };
  },
  onLoad(options) {
    if (options.id) {
      this.announcementId = options.id;
      this.loadAnnouncementDetail();
    } else {
      this.loading = false;
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
    }
  },
  methods: {
    // 加载公告详情
    async loadAnnouncementDetail() {
      try {
        this.loading = true;
        console.log('加载公告详情，ID:', this.announcementId);
        
        const result = await getAnnouncementDetail(this.announcementId);
        console.log('公告详情API返回数据:', result);
        
        if (result) {
          this.announcementDetail = result;
        } else {
          this.announcementDetail = null;
          uni.showToast({
            title: '公告不存在',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载公告详情失败:', error);
        this.announcementDetail = null;
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        '1': '系统公告',
        '2': '活动通知',
        '3': '功能更新',
        '4': '维护通知'
      };
      return typeMap[type] || '通知';
    },

    // 格式化内容
    formatContent(content) {
      if (!content) return '';
      
      // 将换行符转换为<br>标签
      return content.replace(/\n/g, '<br/>');
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          // 如果无法返回，则跳转到公告列表
          uni.redirectTo({
            url: '/pages/announcement/announcement-list'
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.announcement-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.nav-header {
  background-color: #ffffff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.announcement-detail {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .detail-header {
    padding: 30rpx;
    border-bottom: 1px solid #f5f5f5;

    .detail-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }

    .detail-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .detail-type {
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        color: #fff;
        font-size: 22rpx;
        padding: 6rpx 16rpx;
        border-radius: 16rpx;
      }

      .detail-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .detail-content {
    padding: 30rpx;
    font-size: 30rpx;
    color: #333;
    line-height: 1.6;
    
    // rich-text 样式
    :deep(rich-text) {
      word-break: break-all;
    }
  }

  .detail-footer {
    padding: 20rpx 30rpx;
    border-top: 1px solid #f5f5f5;
    background-color: #fafafa;

    .update-info {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .error-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }

  .error-actions {
    margin-top: 40rpx;

    .back-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 28rpx;
      padding: 16rpx 32rpx;
      border-radius: 24rpx;
      text-align: center;
    }
  }
}
</style>
