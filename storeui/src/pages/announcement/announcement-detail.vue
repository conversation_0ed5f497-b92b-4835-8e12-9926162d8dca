<template>
  <view class="announcement-detail-page">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <appHead left fixed title="公告详情"></appHead>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <u-loading-icon mode="flower"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 公告详情 -->
    <view class="announcement-detail" v-else-if="announcementDetail">
      <view class="detail-header">
        <view class="detail-title">{{ announcementDetail.title }}</view>
        <view class="detail-meta">
          <view class="detail-type">{{ getTypeText(announcementDetail.type) }}</view>
          <view class="detail-time">{{ announcementDetail.create_time }}</view>
        </view>
      </view>

      <view class="detail-content">
        <view class="content-wrapper">
          <block v-for="(item, index) in parsedContent" :key="index">
            <text v-if="item.type === 'text'" class="content-text">{{ item.content }}</text>
            <text v-else-if="item.type === 'br'" class="content-br">\n</text>
            <text
              v-else-if="item.type === 'link'"
              class="content-link"
              @tap="handleLinkTap(item.url)"
            >
              {{ item.text }}
            </text>
          </block>
        </view>
      </view>

      <view class="detail-footer">
        <view class="update-info" v-if="announcementDetail.update_time && announcementDetail.update_time !== announcementDetail.create_time">
          更新时间：{{ announcementDetail.update_time }}
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container" v-else>
      <u-icon name="info-circle" color="#ccc" size="120"></u-icon>
      <text class="error-text">公告不存在或已删除</text>
      <view class="error-actions">
        <view class="back-btn" @click="goBack">
          返回列表
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getAnnouncementDetail } from '@/api/announcement.js';

export default {
  data() {
    return {
      announcementId: null,
      announcementDetail: null,
      loading: true
    };
  },
  computed: {
    // 解析公告内容
    parsedContent() {
      if (!this.announcementDetail || !this.announcementDetail.content) {
        return [];
      }
      return this.parseContent(this.announcementDetail.content);
    }
  },
  onLoad(options) {
    if (options.id) {
      this.announcementId = options.id;
      this.loadAnnouncementDetail();
    } else {
      this.loading = false;
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
    }
  },
  methods: {
    // 加载公告详情
    async loadAnnouncementDetail() {
      try {
        this.loading = true;
        console.log('加载公告详情，ID:', this.announcementId);
        
        const result = await getAnnouncementDetail(this.announcementId);
        console.log('公告详情API返回数据:', result);
        
        if (result) {
          this.announcementDetail = result;
        } else {
          this.announcementDetail = null;
          uni.showToast({
            title: '公告不存在',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载公告详情失败:', error);
        this.announcementDetail = null;
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取类型文本
    getTypeText(type) {
      const typeMap = {
        '1': '系统公告',
        '2': '活动通知',
        '3': '功能更新',
        '4': '维护通知'
      };
      return typeMap[type] || '通知';
    },

    // 解析公告内容，将文本和链接分离
    parseContent(content) {
      if (!content) return [];

      try {
        // 确保content是字符串类型
        const contentStr = typeof content === 'string' ? content : String(content);

        const result = [];
        let currentIndex = 0;

        // 正则匹配小程序链接，支持更灵活的格式
        const linkRegex = /<a\s+href=["']?miniapp:\/\/([^"'\s>]+)["']?[^>]*>([^<]+)<\/a>/gi;
        let match;

        // 重置正则表达式的lastIndex
        linkRegex.lastIndex = 0;

        while ((match = linkRegex.exec(contentStr)) !== null) {
          // 添加链接前的文本
          if (match.index > currentIndex) {
            const beforeText = contentStr.substring(currentIndex, match.index);
            this.addTextContent(result, beforeText);
          }

          // 添加链接
          result.push({
            type: 'link',
            url: match[1],
            text: match[2]
          });

          currentIndex = match.index + match[0].length;
        }

        // 添加剩余的文本
        if (currentIndex < contentStr.length) {
          const remainingText = contentStr.substring(currentIndex);
          this.addTextContent(result, remainingText);
        }

        return result;
      } catch (error) {
        console.error('解析公告内容失败:', error);
        // 如果解析失败，返回原始文本
        return [{
          type: 'text',
          content: content || '内容解析失败'
        }];
      }
    },

    // 添加文本内容，处理换行符
    addTextContent(result, text) {
      if (!text) return;

      try {
        // 确保text是字符串
        const textStr = typeof text === 'string' ? text : String(text);

        // 清理文本中的特殊字符
        const cleanText = textStr.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

        const parts = cleanText.split('\n');
        for (let i = 0; i < parts.length; i++) {
          // 只添加非空的文本部分
          if (parts[i].trim()) {
            result.push({
              type: 'text',
              content: parts[i]
            });
          }

          // 添加换行符（除了最后一个部分）
          if (i < parts.length - 1) {
            result.push({
              type: 'br',
              content: '\n'
            });
          }
        }
      } catch (error) {
        console.error('处理文本内容失败:', error);
        // 如果处理失败，直接添加原始文本
        result.push({
          type: 'text',
          content: text || ''
        });
      }
    },

    // 处理链接点击
    handleLinkTap(url) {
      console.log('点击链接:', url);
      this.navigateToMiniappPage(url);
    },

    // 跳转到小程序页面
    navigateToMiniappPage(url) {
      try {
        // 解析URL，支持带参数的页面
        let targetUrl = url;

        // 确保URL以/开头
        if (!targetUrl.startsWith('/')) {
          targetUrl = '/' + targetUrl;
        }

        console.log('准备跳转到页面:', targetUrl);

        wx.navigateTo({
          url: targetUrl,
          success: () => {
            console.log('页面跳转成功:', targetUrl);
          },
          fail: (err) => {
            console.error('navigateTo失败:', err);

            // 如果navigateTo失败，尝试使用switchTab（可能是tabBar页面）
            wx.switchTab({
              url: targetUrl,
              success: () => {
                console.log('Tab页面跳转成功:', targetUrl);
              },
              fail: (tabErr) => {
                console.error('switchTab也失败:', tabErr);
                uni.showToast({
                  title: '页面跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('解析跳转URL失败:', error);
        uni.showToast({
          title: '链接格式错误',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          // 如果无法返回，则跳转到公告列表
          uni.redirectTo({
            url: '/pages/announcement/announcement-list'
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.announcement-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.nav-header {
  background-color: #ffffff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.announcement-detail {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .detail-header {
    padding: 30rpx;
    border-bottom: 1px solid #f5f5f5;

    .detail-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }

    .detail-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .detail-type {
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        color: #fff;
        font-size: 22rpx;
        padding: 6rpx 16rpx;
        border-radius: 16rpx;
      }

      .detail-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .detail-content {
    padding: 30rpx;
    font-size: 30rpx;
    color: #333;
    line-height: 1.6;

    .content-wrapper {
      .content-text {
        font-size: 30rpx;
        color: #333;
        line-height: 1.6;
      }

      .content-br {
        white-space: pre-line;
      }

      .content-link {
        color: #007aff;
        text-decoration: underline;
        font-size: 30rpx;
        line-height: 1.6;
        cursor: pointer;

        &:active {
          opacity: 0.7;
        }
      }
    }
  }

  .detail-footer {
    padding: 20rpx 30rpx;
    border-top: 1px solid #f5f5f5;
    background-color: #fafafa;

    .update-info {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .error-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }

  .error-actions {
    margin-top: 40rpx;

    .back-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 28rpx;
      padding: 16rpx 32rpx;
      border-radius: 24rpx;
      text-align: center;
    }
  }
}
</style>
