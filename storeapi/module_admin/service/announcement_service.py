"""
公告管理服务层
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, and_, or_
from typing import Dict, Any, Optional, List
import json
from datetime import datetime

from utils.log_util import logger
from exceptions.exception import BusinessException, QueryException


class AnnouncementService:
    """公告管理服务类"""

    @classmethod
    async def get_announcement_list_service(
        cls,
        query_db: AsyncSession,
        page: int = 1,
        size: int = 10,
        keyword: Optional[str] = None,
        status: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取公告列表服务

        Args:
            query_db: 数据库会话
            page: 页码
            size: 每页数量
            keyword: 搜索关键词
            status: 状态筛选（保留参数但只显示启用状态的公告）

        Returns:
            包含公告列表和分页信息的字典
        """
        try:
            # 构建查询条件
            where_conditions = []
            params = {}

            # 只显示正常状态（status = '0'）的公告
            where_conditions.append("status = '0'")

            if keyword:
                where_conditions.append("(notice_title LIKE :keyword OR notice_content LIKE :keyword)")
                params['keyword'] = f"%{keyword}%"

            where_clause = " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"""
                SELECT COUNT(*) as total
                FROM sys_notice
                WHERE {where_clause}
            """
            
            count_result = await query_db.execute(text(count_sql), params)
            total = count_result.fetchone().total
            
            # 查询列表数据
            offset = (page - 1) * size
            list_sql = f"""
                SELECT 
                    notice_id,
                    notice_title,
                    notice_type,
                    notice_content,
                    status,
                    create_time,
                    update_time,
                    remark
                FROM sys_notice
                WHERE {where_clause}
                ORDER BY create_time DESC
                LIMIT :limit OFFSET :offset
            """
            
            params.update({
                'limit': size,
                'offset': offset
            })
            
            list_result = await query_db.execute(text(list_sql), params)
            announcements = []
            
            for row in list_result.fetchall():
                # 处理内容字段（可能是blob类型）
                content = cls._decode_content(row.notice_content)

                announcements.append({
                    'id': row.notice_id,
                    'title': row.notice_title,
                    'type': row.notice_type,
                    'content': content,
                    'status': int(row.status),
                    'create_time': row.create_time.strftime('%Y-%m-%d %H:%M:%S') if row.create_time else None,
                    'update_time': row.update_time.strftime('%Y-%m-%d %H:%M:%S') if row.update_time else None,
                    'remark': row.remark
                })
            
            return {
                'list': announcements,
                'total': total,
                'page': page,
                'size': size,
                'pages': (total + size - 1) // size
            }
            
        except Exception as e:
            logger.error(f"获取公告列表服务异常: {str(e)}")
            raise QueryException(message=f"获取公告列表失败: {str(e)}")

    @classmethod
    async def get_announcement_detail_service(
        cls,
        query_db: AsyncSession,
        announcement_id: int
    ) -> Dict[str, Any]:
        """
        获取公告详情服务

        Args:
            query_db: 数据库会话
            announcement_id: 公告ID

        Returns:
            公告详情字典
        """
        try:
            sql = """
                SELECT
                    notice_id,
                    notice_title,
                    notice_type,
                    notice_content,
                    status,
                    create_time,
                    update_time,
                    remark
                FROM sys_notice
                WHERE notice_id = :announcement_id AND status = '0'
            """
            
            result = await query_db.execute(text(sql), {'announcement_id': announcement_id})
            row = result.fetchone()
            
            if not row:
                raise BusinessException(message="公告不存在")

            # 处理内容字段（可能是blob类型）
            content = cls._decode_content(row.notice_content)

            return {
                'id': row.notice_id,
                'title': row.notice_title,
                'type': row.notice_type,
                'content': content,
                'status': int(row.status),
                'create_time': row.create_time.strftime('%Y-%m-%d %H:%M:%S') if row.create_time else None,
                'update_time': row.update_time.strftime('%Y-%m-%d %H:%M:%S') if row.update_time else None,
                'remark': row.remark
            }
            
        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"获取公告详情服务异常: {str(e)}")
            raise QueryException(message=f"获取公告详情失败: {str(e)}")

    @classmethod
    async def get_latest_announcement_service(
        cls,
        query_db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """
        获取最新公告服务（用于首页点击跳转）
        
        Args:
            query_db: 数据库会话
            
        Returns:
            最新公告字典或None
        """
        try:
            sql = """
                SELECT 
                    notice_id,
                    notice_title,
                    notice_type,
                    notice_content,
                    status,
                    create_time,
                    update_time,
                    remark
                FROM sys_notice
                WHERE status = '0'
                ORDER BY create_time DESC
                LIMIT 1
            """
            
            result = await query_db.execute(text(sql))
            row = result.fetchone()
            
            if not row:
                return None

            # 处理内容字段（可能是blob类型）
            content = cls._decode_content(row.notice_content)

            return {
                'id': row.notice_id,
                'title': row.notice_title,
                'type': row.notice_type,
                'content': content,
                'status': int(row.status),
                'create_time': row.create_time.strftime('%Y-%m-%d %H:%M:%S') if row.create_time else None,
                'update_time': row.update_time.strftime('%Y-%m-%d %H:%M:%S') if row.update_time else None,
                'remark': row.remark
            }
            
        except Exception as e:
            logger.error(f"获取最新公告服务异常: {str(e)}")
            raise QueryException(message=f"获取最新公告失败: {str(e)}")

    @classmethod
    async def get_home_announcements_service(
        cls,
        query_db: AsyncSession,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        获取首页公告列表服务（滚动播报用）
        
        Args:
            query_db: 数据库会话
            limit: 获取数量
            
        Returns:
            公告列表
        """
        try:
            sql = """
                SELECT 
                    notice_id,
                    notice_title,
                    notice_content,
                    create_time
                FROM sys_notice
                WHERE status = '0'
                ORDER BY create_time DESC
                LIMIT :limit
            """
            
            result = await query_db.execute(text(sql), {'limit': limit})
            announcements = []
            
            for row in result.fetchall():
                # 处理内容字段（可能是blob类型）
                content = cls._decode_content(row.notice_content)

                # 计算时间显示
                time_display = cls._format_time_display(row.create_time)

                # 清理内容中的HTML标签，用于首页显示
                clean_content = cls._clean_html_tags(content)

                announcements.append({
                    'id': row.notice_id,
                    'title': row.notice_title,
                    'content': clean_content[:50] + '...' if len(clean_content) > 50 else clean_content,  # 截取前50个字符
                    'time': time_display
                })
            
            return announcements
            
        except Exception as e:
            logger.error(f"获取首页公告列表服务异常: {str(e)}")
            raise QueryException(message=f"获取首页公告列表失败: {str(e)}")

    @staticmethod
    def _decode_content(content) -> str:
        """
        统一处理内容字段的解码

        Args:
            content: 可能是字符串或bytes的内容

        Returns:
            解码后的字符串
        """
        if content is None:
            return ""

        if isinstance(content, bytes):
            try:
                return content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    return content.decode('gbk')
                except UnicodeDecodeError:
                    logger.warning("无法解码内容，使用错误替换模式")
                    return content.decode('utf-8', errors='replace')

        if isinstance(content, str):
            return content

        # 如果是其他类型，尝试转换为字符串
        return str(content)

    @staticmethod
    def _clean_html_tags(content: str) -> str:
        """
        清理HTML标签，用于首页显示

        Args:
            content: 包含HTML标签的内容

        Returns:
            清理后的纯文本内容
        """
        if not content:
            return ""

        import re
        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', content)
        # 移除多余的空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        return clean_text

    @staticmethod
    def _format_time_display(create_time: datetime) -> str:
        """
        格式化时间显示

        Args:
            create_time: 创建时间

        Returns:
            格式化后的时间字符串
        """
        if not create_time:
            return ""

        now = datetime.now()
        diff = now - create_time

        if diff.days > 0:
            if diff.days == 1:
                return "昨天"
            elif diff.days < 7:
                return f"{diff.days}天前"
            else:
                return create_time.strftime('%m-%d')
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}小时前"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
