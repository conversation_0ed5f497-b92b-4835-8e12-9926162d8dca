from fastapi import APIRouter, Depends, Request, Query, Form, Body, Header
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from pydantic import BaseModel, validator
from decimal import Decimal
from datetime import date, time, datetime
from typing import Union
import re
from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.order_service import OrderService
from module_admin.service.schedule_service import ScheduleService
from module_admin.service.user_balance_service import UserBalanceService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.service_staff_login_service import ServiceStaffLoginService
from utils.log_util import logger
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# ==================== 代客下单请求模型定义 ====================

class OrderInfoModel(BaseModel):
    """基础订单信息"""
    store_uuid: Optional[str] = None  # 改为可选，后端从current_user获取
    amount: Union[Decimal, int, float]  # 支持多种数字类型
    buy_num: Union[Decimal, int, float] = 1.0  # 支持多种数字类型
    pay_type: str = "1"
    service_date: Union[date, str]  # 支持字符串和date类型
    service_time: Union[time, str]  # 支持字符串和time类型

    @validator('amount')
    def validate_amount(cls, v):
        # 转换为Decimal类型
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v <= 0:
            raise ValueError('购买金额必须大于0')
        return v

    @validator('buy_num')
    def validate_buy_num(cls, v):
        # 转换为Decimal类型
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v <= 0:
            raise ValueError('购买数量必须大于0')
        return v

    @validator('service_date')
    def validate_service_date(cls, v):
        # 如果是字符串，转换为date对象
        if isinstance(v, str):
            try:
                v = datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError('服务日期格式不正确，应为YYYY-MM-DD')

        if v < date.today():
            raise ValueError('服务日期不能早于今天')
        return v

    @validator('service_time')
    def validate_service_time(cls, v):
        # 如果是字符串，转换为time对象
        if isinstance(v, str):
            try:
                v = datetime.strptime(v, '%H:%M').time()
            except ValueError:
                raise ValueError('服务时间格式不正确，应为HH:MM')
        return v

class CustomerInfoModel(BaseModel):
    """客户信息"""
    name: str
    phone: str
    original_input: Optional[str] = None

    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) < 1 or len(v.strip()) > 20:
            raise ValueError('客户姓名长度必须在1-20个字符之间')
        return v.strip()

    @validator('phone')
    def validate_phone(cls, v):
        if not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v

class AddressInfoModel(BaseModel):
    """地址信息"""
    address: str
    door_number: Optional[str] = None
    longitude: Union[Decimal, int, float]  # 支持多种数字类型
    latitude: Union[Decimal, int, float]   # 支持多种数字类型
    city: Optional[str] = None
    province: Optional[str] = None
    district: Optional[str] = None

    @validator('address')
    def validate_address(cls, v):
        if not v or len(v.strip()) < 5 or len(v.strip()) > 200:
            raise ValueError('地址长度必须在5-200个字符之间')
        return v.strip()

    @validator('longitude')
    def validate_longitude(cls, v):
        # 转换为Decimal类型
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v < -180 or v > 180:
            raise ValueError('经度值必须在-180到180之间')
        return v

    @validator('latitude')
    def validate_latitude(cls, v):
        # 转换为Decimal类型
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v < -90 or v > 90:
            raise ValueError('纬度值必须在-90到90之间')
        return v

class ProductInfoModel(BaseModel):
    """产品SKU信息"""
    product_uuid: str
    sku_id: int
    sku_name: Optional[str] = None
    sku_price: Optional[Union[Decimal, int, float]] = None  # 支持多种数字类型
    sku_commission: Optional[Union[Decimal, int, float]] = None  # 支持多种数字类型
    sku_commission_type: Optional[int] = None

    @validator('sku_id')
    def validate_sku_id(cls, v):
        if v <= 0:
            raise ValueError('SKU ID必须大于0')
        return v

    @validator('sku_price')
    def validate_sku_price(cls, v):
        if v is not None and isinstance(v, (int, float)):
            v = Decimal(str(v))
        return v

    @validator('sku_commission')
    def validate_sku_commission(cls, v):
        if v is not None and isinstance(v, (int, float)):
            v = Decimal(str(v))
        return v

class RemarkInfoModel(BaseModel):
    """备注信息"""
    service_remark: Optional[str] = None
    customer_note: Optional[str] = None
    after_sale_remark: Optional[str] = None

class BusinessInfoModel(BaseModel):
    """扩展业务信息"""
    sales_attribution: Optional[str] = None
    sales_attribution_staff_id: Optional[str] = None  # 销售归属员工ID
    sales_attribution_staff_name: Optional[str] = None  # 销售归属员工姓名
    sales_commission: Optional[Union[Decimal, int, float, str]] = None  # 支持空字符串
    channel_code: Optional[str] = None
    order_source: Optional[str] = None
    area: Optional[str] = None
    house_type: Optional[str] = None

    @validator('sales_commission')
    def validate_sales_commission(cls, v):
        # 处理空字符串或None
        if v is None or v == "" or v == "":
            return None
        # 转换为Decimal类型
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        elif isinstance(v, str):
            try:
                v = Decimal(v)
            except:
                return None  # 无法转换的字符串返回None
        return v

class ProxyOrderCreateModel(BaseModel):
    """代客下单请求模型"""
    order_info: OrderInfoModel
    customer_info: CustomerInfoModel
    address_info: AddressInfoModel
    product_info: ProductInfoModel
    remark_info: Optional[RemarkInfoModel] = None
    business_info: Optional[BusinessInfoModel] = None

# ==================== 订单修改请求模型定义 ====================

class UpdateOrderAmountRequest(BaseModel):
    """订单改价请求模型"""
    order_number: str
    new_amount: Union[Decimal, int, float]

    @validator('order_number')
    def validate_order_number(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('订单编号不能为空')
        return v.strip()

    @validator('new_amount')
    def validate_new_amount(cls, v):
        # 转换为Decimal类型
        if isinstance(v, (int, float)):
            v = Decimal(str(v))
        if v <= 0:
            raise ValueError('订单金额必须大于0')
        if v > Decimal('99999.99'):
            raise ValueError('订单金额不能超过99999.99')
        # 确保保留两位小数
        return v.quantize(Decimal('0.01'))

class UpdateOrderTimeRequest(BaseModel):
    """订单改时请求模型"""
    order_number: str
    new_service_date: str

    @validator('order_number')
    def validate_order_number(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('订单编号不能为空')
        return v.strip()

    @validator('new_service_date')
    def validate_new_service_date(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('服务时间不能为空')

        # 验证时间格式：YYYY-MM-DD HH:00:00
        import re
        pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:00:00$'
        if not re.match(pattern, v.strip()):
            raise ValueError('服务时间格式必须为：YYYY-MM-DD HH:00:00（如：2025-06-06 09:00:00）')

        # 验证时间是否为未来时间
        try:
            from datetime import datetime
            service_datetime = datetime.strptime(v.strip(), '%Y-%m-%d %H:%M:%S')
            if service_datetime <= datetime.now():
                raise ValueError('服务时间必须为未来时间')
        except ValueError as e:
            if '服务时间必须为未来时间' in str(e):
                raise e
            raise ValueError('服务时间格式不正确')

        return v.strip()

# 统一的用户身份识别函数
async def get_current_user_or_staff(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
    authorization: str = Header(None)
):
    """
    统一获取当前用户（支持门店端和员工端）
    """
    if not authorization:
        raise ValidationException(message="缺少授权令牌")

    # 提取token
    token = authorization
    if authorization.startswith('Bearer '):
        token = authorization[7:]

    # 通过解析JWT token来判断用户类型
    import jwt
    from config.env import JwtConfig

    try:
        # 解析token获取用户类型
        logger.info(f"开始解析JWT token，token长度: {len(token)}")
        payload = jwt.decode(token, JwtConfig.jwt_secret_key, algorithms=[JwtConfig.jwt_algorithm])
        user_type = payload.get('user_type', 'store')  # 默认为门店端

        logger.info(f"JWT解析成功，完整payload: {payload}")
        logger.info(f"用户类型: {user_type}, 用户ID: {payload.get('user_id')}")

        if user_type == 'staff':
            # 员工端验证
            logger.info("检测到员工端token，开始员工验证")
            staff = await ServiceStaffLoginService.get_current_staff_for_proxy_order(token, request, query_db)
            # 将员工信息包装成类似门店端用户的结构
            class StaffWrapper:
                def __init__(self, staff):
                    self.user = staff

            logger.info(f"员工端验证成功，员工: {staff.real_name}, 门店UUID: {staff.store_uuid}")
            return StaffWrapper(staff)
        else:
            # 门店端验证
            logger.info(f"检测到门店端token (user_type={user_type})，使用门店端验证")
            return await InternalUserLoginService.get_current_user(request, token, query_db)

    except (jwt.InvalidTokenError, jwt.ExpiredSignatureError, jwt.DecodeError) as e:
        logger.error(f"JWT token解析失败: {str(e)}")
        raise ValidationException(message="无效的访问令牌")
    except Exception as e:
        logger.error(f"身份验证异常: {str(e)}")
        raise ValidationException(message="身份验证失败")

# 使用统一的API路由前缀格式
order_controller = APIRouter(prefix='/api/v1/order')

@order_controller.post('/findOrderList', summary="获取订单列表")
async def find_order_list(
    page: str = Form("1"),
    size: str = Form("20"),
    keyword: Optional[str] = Form(None, description="搜索关键词（订单ID、客户姓名/手机号）"),
    order_status: Optional[str] = Form(None, description="订单状态筛选"),
    product_type: Optional[str] = Form(None, description="产品类型"),
    order_new: Optional[str] = Form(None, description="订单新旧状态"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    获取订单列表

    :param db: 数据库会话
    :param current_user: 当前用户信息
    :param page: 页码
    :param size: 每页数量
    :param keyword: 搜索关键词（订单ID、客户姓名/手机号）
    :param order_status: 订单状态筛选
    :param product_type: 产品类型
    :param order_new: 订单新旧状态
    :return: 订单列表和分页信息
    """
    try:
        # 获取当前用户的门店信息
        store_uuid = current_user.user.store_uuid if current_user.user else None
        if not store_uuid:
            user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None) or getattr(current_user.user, 'id', 'unknown')
            logger.warning(f"当前用户没有门店信息: user_id={user_id}")
            return ResponseUtil.error(msg="当前用户没有关联的门店信息", code=400)

        # 转换参数类型
        page_num = int(page)
        page_size = int(size)

        # 调用服务层方法 - 传递门店UUID和筛选参数
        result = await OrderService.find_order_list_service(
            db, page_num, page_size, store_uuid, keyword, order_status, product_type, order_new
        )

        return ResponseUtil.success(data=result)
    except QueryException as e:
        logger.error(f"获取订单列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取订单列表异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取订单列表失败: {str(e)}", code=500)


@order_controller.get('/findServeOrderDetail', summary="获取服务订单详情")
async def find_serve_order_detail(
    request: Request,
    order_number: str = Query(..., description="订单编号"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取服务订单详情接口

    根据订单编号获取服务订单的详细信息

    原始API路径: /client/findServeOrderDetail
    """
    try:
        # 调用服务层获取订单详情
        result = await OrderService.find_serve_order_detail_service(query_db, order_number)

        return ResponseUtil.success(
            msg="获取服务订单详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询订单详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取订单详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取订单详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/createOrder', summary="创建订单")
async def create_order(
    request: Request,
    ext_isset: str = Form("1", description="扩展信息是否设置标志"),
    address: str = Form(..., description="服务地址"),
    lng: str = Form(..., description="经度"),
    lat: str = Form(..., description="纬度"),
    address_desc: str = Form("", description="地址描述"),
    city: str = Form(..., description="城市代码"),
    user_name: str = Form(..., description="用户名"),
    bd_city_name: str = Form("", description="百度地图城市名称"),
    mobile: str = Form(..., description="用户手机号"),
    address_id: str = Form(..., description="地址ID"),
    service_remark: str = Form("", description="服务备注"),
    store_uuid: str = Form(..., description="店铺唯一标识"),
    product_uuid: str = Form(..., description="产品唯一标识"),
    buy_num: str = Form(..., description="购买数量"),
    bd_province_name: str = Form("", description="百度地图省份名称"),
    address_user_id: str = Form(..., description="地址用户ID"),
    bd_area_name: str = Form("", description="百度地图区域名称"),
    pay_actual: str = Form(..., description="实际支付金额"),
    after_sale_remark: str = Form("", description="售后备注"),
    pay_type: str = Form(..., description="支付类型"),
    is_fictitious_nub: str = Form("0", description="是否为虚拟号码"),
    order_ext: Optional[str] = Form(None, description="订单扩展信息"),
    send_sms: str = Form("0", description="是否发送短信"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """创建订单接口

    此接口用于创建新的服务订单。

    原始API路径: /client/createOrder
    """
    try:
        # 构建订单数据
        order_data = {
            "ext_isset": ext_isset,
            "address": address,
            "lng": lng,
            "lat": lat,
            "address_desc": address_desc,
            "city": city,
            "user_name": user_name,
            "bd_city_name": bd_city_name,
            "mobile": mobile,
            "address_id": address_id,
            "service_remark": service_remark,
            "store_uuid": store_uuid,
            "product_uuid": product_uuid,
            "buy_num": buy_num,
            "bd_province_name": bd_province_name,
            "address_user_id": address_user_id,
            "bd_area_name": bd_area_name,
            "pay_actual": pay_actual,
            "after_sale_remark": after_sale_remark,
            "pay_type": pay_type,
            "is_fictitious_nub": is_fictitious_nub,
            "order_ext": order_ext,
            "send_sms": send_sms
        }

        # 调用服务层创建订单
        result = await OrderService.create_order_service(query_db, order_data)

        return ResponseUtil.success(
            msg="创建订单成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"创建订单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/updateServeOrder', summary="更新服务订单")
async def update_serve_order(
    request: Request,
    order_number: str = Form(..., description="订单编号"),
    service_time: Optional[str] = Form(None, description="服务时间"),
    service_address: Optional[str] = Form(None, description="服务地址"),
    remark: Optional[str] = Form(None, description="备注"),
    service_remark: Optional[str] = Form(None, description="服务备注"),
    order_status: Optional[str] = Form(None, description="订单状态"),
    buy_num: Optional[int] = Form(None, description="购买数量"),
    service_personal_commission: Optional[str] = Form(None, description="服务人员佣金"),
    service_personal: Optional[str] = Form(None, description="服务人员ID"),
    is_host: Optional[str] = Form(None, description="是否为主机"),
    send_sms: Optional[str] = Form("0", description="是否发送短信"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新服务订单接口

    更新服务订单的信息

    原始API路径: /client/updateServeOrder
    """
    try:
        # 构建更新数据
        update_data = {}
        if service_time is not None:
            update_data["service_time"] = service_time
        if service_address is not None:
            update_data["service_address"] = service_address
        if remark is not None:
            update_data["remark"] = remark
        if service_remark is not None:
            update_data["service_remark"] = service_remark
        if order_status is not None:
            update_data["order_status"] = order_status
        if buy_num is not None:
            update_data["buy_num"] = buy_num
        if service_personal is not None:
            update_data["service_personal"] = service_personal
        if service_personal_commission is not None:
            update_data["service_personal_commission"] = service_personal_commission
        if is_host is not None:
            update_data["is_host"] = is_host
        if send_sms is not None:
            update_data["send_sms"] = send_sms

        # 调用服务层更新订单
        result = await OrderService.update_serve_order_service(query_db, order_number, update_data)

        return ResponseUtil.success(
            msg="更新服务订单成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"更新订单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/findServeOrderList', summary="获取服务订单列表")
async def find_serve_order_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="订单状态"),
    commission_status: Optional[str] = Query(None, description="佣金状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取服务订单列表接口

    获取服务订单列表，支持分页和状态筛选

    原始API路径: /client/findServeOrderList
    """
    try:
        # 调用服务层获取服务订单列表
        result = await OrderService.find_serve_order_list_service(query_db, page, size, status, commission_status)

        return ResponseUtil.success(
            msg="获取服务订单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询服务订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取服务订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取服务订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/scheduleList', summary="获取排班列表")
async def schedule_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    job_type: Optional[str] = Query(None, description="工作类型"),
    serviceDate: Optional[str] = Query(None, description="服务日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取排班列表接口

    获取排班列表，支持分页和筛选

    原始API路径: /client/scheduleList
    """
    try:
        # 调用服务层获取排班列表
        result = await ScheduleService.find_schedule_list_service(query_db, page, size, job_type, serviceDate)

        return ResponseUtil.success(
            msg="获取排班列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询排班列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取排班列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取排班列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/getUserBalance', summary="获取用户余额")
async def get_user_balance(
    request: Request,
    user_id: str = Form(..., description="用户ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取用户余额接口

    获取用户的余额信息

    原始API路径: /client/getUserBalance
    """
    try:
        # 调用服务层获取用户余额
        result = await UserBalanceService.get_user_balance_service(query_db, user_id)

        return ResponseUtil.success(
            msg="获取用户余额成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取用户余额参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询用户余额失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取用户余额业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取用户余额异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/cancelServeOrder', summary="取消服务订单")
async def cancel_serve_order(
    request: Request,
    order_number: str = Form(..., description="订单编号"),
    cancel_reason: Optional[str] = Form(None, description="取消原因"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """取消服务订单接口

    取消指定的服务订单

    原始API路径: /client/cancelServeOrder
    """
    try:
        # 调用服务层取消服务订单
        result = await OrderService.cancel_serve_order_service(query_db, order_number, cancel_reason)

        return ResponseUtil.success(
            msg="取消服务订单成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"服务订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"取消服务订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"取消服务订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/setPresetCommission', summary="设置预设佣金")
async def set_preset_commission(
    request: Request,
    order_number: str = Form(..., description="订单编号"),
    preset_commission: float = Form(..., description="预设佣金金额"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """设置预设佣金接口

    为订单设置预设佣金，用于分享接单模式
    """
    try:
        # 调用服务层设置预设佣金
        result = await OrderService.set_preset_commission_service(
            query_db, order_number, preset_commission, current_user.user.store_uuid
        )

        return ResponseUtil.success(
            msg="设置预设佣金成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"设置预设佣金验证异常: {e.message}")
        return ResponseUtil.error(msg=e.message, code=400)
    except ResourceNotFoundException as e:
        logger.error(f"设置预设佣金资源不存在: {e.message}")
        return ResponseUtil.error(msg=e.message, code=404)
    except BusinessException as e:
        logger.error(f"设置预设佣金业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message, code=500)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"设置预设佣金异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@order_controller.post('/shareRobOrder', summary="分享抢单")
async def share_rob_order(
    request: Request,
    order_number: str = Form(..., description="订单编号"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """分享抢单接口

    生成分享抢单的链接和二维码

    原始API路径: /client/shareRobOrder
    """
    try:
        # 调用服务层生成分享抢单信息
        result = await OrderService.share_rob_order_service(query_db, order_number)

        return ResponseUtil.success(
            msg="生成分享抢单信息成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"分享抢单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"分享抢单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/setMealOrderSearchByVue', summary="通过Vue搜索套餐订单")
async def set_meal_order_search_by_vue(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    keyword: Optional[str] = Form(None, description="关键词"),
    status: Optional[str] = Form(None, description="订单状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """通过Vue搜索套餐订单接口

    分页获取套餐订单列表，支持关键词和状态筛选

    原始API路径: /client/setMealOrderSearchByVue
    """
    try:
        # 调用服务层搜索套餐订单
        result = await OrderService.set_meal_order_search_service(query_db, page, size, keyword, status)

        return ResponseUtil.success(
            msg="搜索套餐订单成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询套餐订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"搜索套餐订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"搜索套餐订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/editJobOrderSaveByVue', summary="通过Vue编辑并保存工单")
async def edit_job_order_save_by_vue(
    request: Request,
    order_number: str = Form(..., description="订单编号"),
    job_order_data: Dict = Body(..., description="工单数据"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """通过Vue编辑并保存工单接口

    编辑并保存工单信息

    原始API路径: /client/editJobOrderSaveByVue
    """
    try:
        # 调用服务层编辑并保存工单
        result = await OrderService.edit_job_order_save_service(query_db, order_number, job_order_data)

        return ResponseUtil.success(
            msg="编辑并保存工单成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"工单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"工单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"编辑工单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"编辑工单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/setMealOrderForVue', summary="获取用户套餐订单信息")
async def set_meal_order_for_vue(
    request: Request,
    user_id: str = Query(..., description="用户ID"),
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取用户套餐订单信息接口

    获取指定用户的套餐订单信息

    原始API路径: /client/setMealOrderForVue
    """
    try:
        # 调用服务层获取用户套餐订单信息
        result = await OrderService.set_meal_order_for_vue_service(query_db, user_id, page, size)

        return ResponseUtil.success(
            msg="获取用户套餐订单信息成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"用户资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询用户套餐订单信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取用户套餐订单信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取用户套餐订单信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/jobOrderForVue', summary="获取Vue端工单列表")
async def job_order_for_vue(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="工单状态"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取Vue端工单列表接口

    分页获取工单列表，支持状态和关键词筛选

    原始API路径: /client/jobOrderForVue
    """
    try:
        # 调用服务层获取工单列表
        result = await OrderService.job_order_for_vue_service(query_db, page, size, status, keyword)

        return ResponseUtil.success(
            msg="获取工单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询工单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取工单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取工单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加产品相关接口
@order_controller.get('/productList', summary="获取产品列表")
async def product_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="产品状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取产品列表接口

    分页获取产品列表，支持状态筛选

    原始API路径: /client/productList
    """
    try:
        # 调用服务层获取产品列表
        result = await OrderService.product_list_service(query_db, page, size, status)

        return ResponseUtil.success(
            msg="获取产品列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询产品列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取产品列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取产品列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/productNewList', summary="获取新产品列表")
async def product_new_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="产品状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取新产品列表接口

    分页获取新产品列表，支持状态筛选

    原始API路径: /client/productNewList
    """
    try:
        # 调用服务层获取新产品列表
        result = await OrderService.product_new_list_service(query_db, page, size, status)

        return ResponseUtil.success(
            msg="获取新产品列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询新产品列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取新产品列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取新产品列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/refreshMiniQr', summary="刷新小程序二维码")
async def refresh_mini_qr(
    request: Request,
    product_id: str = Form(..., description="产品ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """刷新小程序二维码接口

    刷新指定产品的小程序二维码

    原始API路径: /client/refreshMiniQr
    """
    try:
        # 调用服务层刷新小程序二维码
        result = await OrderService.refresh_mini_qr_service(query_db, product_id)

        return ResponseUtil.success(
            msg="刷新小程序二维码成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"产品资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"刷新小程序二维码业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"刷新小程序二维码异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/productListSearch', summary="获取产品列表搜索")
async def product_list_search(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    keyword: Optional[str] = Form(None, description="关键词"),
    status: Optional[str] = Form(None, description="产品状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取产品列表搜索接口

    分页获取产品列表，支持关键词和状态筛选

    原始API路径: /client/productListSearch
    """
    try:
        # 调用服务层搜索产品列表
        result = await OrderService.product_list_search_service(query_db, page, size, keyword, status)

        return ResponseUtil.success(
            msg="搜索产品列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询产品列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"搜索产品列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"搜索产品列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/getMiniStyleBaseInfo', summary="获取小程序样式基本信息")
async def get_mini_style_base_info(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取小程序样式基本信息接口

    获取小程序的样式基本信息

    原始API路径: /client/getMiniStyleBaseInfo
    """
    try:
        # 调用服务层获取小程序样式基本信息
        result = await OrderService.get_mini_style_base_info_service(query_db)

        return ResponseUtil.success(
            msg="获取小程序样式基本信息成功",
            data=result
        )
    except BusinessException as e:
        logger.error(f"获取小程序样式基本信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取小程序样式基本信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加员工相关接口
@order_controller.get('/staffList', summary="获取员工列表")
async def staff_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="员工状态"),
    job_type: Optional[str] = Query(None, description="工作类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取员工列表接口

    分页获取员工列表，支持状态和工作类型筛选

    原始API路径: /client/staffList
    """
    try:
        # 调用服务层获取员工列表
        from module_admin.service.staff_service import StaffService
        result = await StaffService.staff_list_service(query_db, page, size, status, job_type)

        return ResponseUtil.success(
            msg="获取员工列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询员工列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取员工列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取员工列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/staffListSearch', summary="搜索员工列表")
async def staff_list_search(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    keyword: Optional[str] = Form(None, description="关键词"),
    status: Optional[str] = Form(None, description="员工状态"),
    job_type: Optional[str] = Form(None, description="工作类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """搜索员工列表接口

    分页搜索员工列表，支持关键词、状态和工作类型筛选

    原始API路径: /client/staffListSearch
    """
    try:
        # 调用服务层搜索员工列表
        from module_admin.service.staff_service import StaffService
        result = await StaffService.staff_list_search_service(query_db, page, size, keyword, status, job_type)

        return ResponseUtil.success(
            msg="搜索员工列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询员工列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"搜索员工列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"搜索员工列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/staffDetail', summary="获取员工详情")
async def staff_detail(
    request: Request,
    staff_id: str = Query(..., description="员工ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取员工详情接口

    获取指定员工的详细信息

    原始API路径: /client/staffDetail
    """
    try:
        # 调用服务层获取员工详情
        from module_admin.service.staff_service import StaffService
        result = await StaffService.staff_detail_service(query_db, staff_id)

        return ResponseUtil.success(
            msg="获取员工详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"员工资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询员工详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取员工详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取员工详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/addStaff', summary="添加员工")
async def add_staff(
    request: Request,
    staff_data: Dict = Body(..., description="员工数据"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """添加员工接口

    添加新员工

    原始API路径: /client/addStaff
    """
    try:
        # 调用服务层添加员工
        from module_admin.service.staff_service import StaffService
        result = await StaffService.add_staff_service(query_db, staff_data)

        return ResponseUtil.success(
            msg="添加员工成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"员工数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"添加员工业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"添加员工异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/updateStaff', summary="更新员工信息")
async def update_staff(
    request: Request,
    update_data: Dict = Body(..., description="更新数据，包含staff_id和staff_data"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新员工信息接口

    更新指定员工的信息

    原始API路径: /client/updateStaff
    """
    try:
        # 从请求数据中提取参数
        staff_id = update_data.get('staff_id')
        staff_data = update_data.get('staff_data')

        if not staff_id:
            raise ValidationException(message="员工ID不能为空")
        if not staff_data:
            raise ValidationException(message="员工数据不能为空")

        # 调用服务层更新员工信息
        from module_admin.service.staff_service import StaffService
        result = await StaffService.update_staff_service(query_db, staff_id, staff_data)

        return ResponseUtil.success(
            msg="更新员工信息成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"员工资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"员工数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新员工信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新员工信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加客户相关接口
@order_controller.get('/customerList', summary="获取客户列表")
async def customer_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="客户状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取客户列表接口

    分页获取客户列表，支持状态筛选

    原始API路径: /client/customerList
    """
    try:
        # 调用服务层获取客户列表
        from module_admin.service.customer_service import CustomerService
        result = await CustomerService.customer_list_service(query_db, page, size, status)

        return ResponseUtil.success(
            msg="获取客户列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询客户列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/customerListSearch', summary="搜索客户列表")
async def customer_list_search(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    keyword: Optional[str] = Form(None, description="关键词"),
    status: Optional[str] = Form(None, description="客户状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """搜索客户列表接口

    分页搜索客户列表，支持关键词和状态筛选

    原始API路径: /client/customerListSearch
    """
    try:
        # 调用服务层搜索客户列表
        from module_admin.service.customer_service import CustomerService
        result = await CustomerService.customer_list_search_service(query_db, page, size, keyword, status)

        return ResponseUtil.success(
            msg="搜索客户列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询客户列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"搜索客户列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"搜索客户列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/customerDetail', summary="获取客户详情")
async def customer_detail(
    request: Request,
    customer_id: str = Query(..., description="客户ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取客户详情接口

    获取指定客户的详细信息

    原始API路径: /client/customerDetail
    """
    try:
        # 调用服务层获取客户详情
        from module_admin.service.customer_service import CustomerService
        result = await CustomerService.customer_detail_service(query_db, customer_id)

        return ResponseUtil.success(
            msg="获取客户详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"客户资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询客户详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/addCustomer', summary="添加客户")
async def add_customer(
    request: Request,
    customer_data: Dict = Body(..., description="客户数据"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """添加客户接口

    添加新客户

    原始API路径: /client/addCustomer
    """
    try:
        # 调用服务层添加客户
        from module_admin.service.customer_service import CustomerService
        result = await CustomerService.add_customer_service(query_db, customer_data)

        return ResponseUtil.success(
            msg="添加客户成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"客户数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"添加客户业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"添加客户异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/updateCustomer', summary="更新客户信息")
async def update_customer(
    request: Request,
    customer_id: str = Form(..., description="客户ID"),
    customer_data: Dict = Body(..., description="客户数据"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新客户信息接口

    更新指定客户的信息

    原始API路径: /client/updateCustomer
    """
    try:
        # 调用服务层更新客户信息
        from module_admin.service.customer_service import CustomerService
        result = await CustomerService.update_customer_service(query_db, customer_id, customer_data)

        return ResponseUtil.success(
            msg="更新客户信息成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"客户资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"客户数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新客户信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新客户信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加消息相关接口
@order_controller.get('/messageList', summary="获取消息列表")
async def message_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    is_read: Optional[str] = Query(None, description="是否已读"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取消息列表接口

    分页获取消息列表，支持已读状态筛选

    原始API路径: /client/messageList
    """
    try:
        # 调用服务层获取消息列表
        from module_admin.service.message_service import MessageService
        result = await MessageService.message_list_service(query_db, page, size, is_read)

        return ResponseUtil.success(
            msg="获取消息列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询消息列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取消息列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取消息列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/readMessage', summary="标记消息为已读")
async def read_message(
    request: Request,
    message_id: str = Form(..., description="消息ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """标记消息为已读接口

    将指定消息标记为已读状态

    原始API路径: /client/readMessage
    """
    try:
        # 调用服务层标记消息为已读
        from module_admin.service.message_service import MessageService
        result = await MessageService.read_message_service(query_db, message_id)

        return ResponseUtil.success(
            msg="标记消息为已读成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"消息资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"标记消息为已读业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"标记消息为已读异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/readAllMessages', summary="标记所有消息为已读")
async def read_all_messages(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """标记所有消息为已读接口

    将所有未读消息标记为已读状态

    原始API路径: /client/readAllMessages
    """
    try:
        # 调用服务层标记所有消息为已读
        from module_admin.service.message_service import MessageService
        result = await MessageService.read_all_messages_service(query_db)

        return ResponseUtil.success(
            msg="标记所有消息为已读成功",
            data=result
        )
    except BusinessException as e:
        logger.error(f"标记所有消息为已读业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"标记所有消息为已读异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/messageDetail', summary="获取消息详情")
async def message_detail(
    request: Request,
    message_id: str = Query(..., description="消息ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取消息详情接口

    获取指定消息的详细信息

    原始API路径: /client/messageDetail
    """
    try:
        # 调用服务层获取消息详情
        from module_admin.service.message_service import MessageService
        result = await MessageService.message_detail_service(query_db, message_id)

        return ResponseUtil.success(
            msg="获取消息详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"消息资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询消息详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取消息详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取消息详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/deleteMessage', summary="删除消息")
async def delete_message(
    request: Request,
    message_id: str = Form(..., description="消息ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除消息接口

    删除指定消息

    原始API路径: /client/deleteMessage
    """
    try:
        # 调用服务层删除消息
        from module_admin.service.message_service import MessageService
        result = await MessageService.delete_message_service(query_db, message_id)

        return ResponseUtil.success(
            msg="删除消息成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"消息资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除消息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除消息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加评价相关接口
@order_controller.get('/evaluationList', summary="获取评价列表")
async def evaluation_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    status: Optional[str] = Query(None, description="评价状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价列表接口

    分页获取评价列表，支持状态筛选

    原始API路径: /client/evaluationList
    """
    try:
        # 调用服务层获取评价列表
        from module_admin.service.evaluation_service import EvaluationService
        result = await EvaluationService.evaluation_list_service(query_db, page, size, status)

        return ResponseUtil.success(
            msg="获取评价列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询评价列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/evaluationListSearch', summary="搜索评价列表")
async def evaluation_list_search(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    keyword: Optional[str] = Form(None, description="关键词"),
    status: Optional[str] = Form(None, description="评价状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """搜索评价列表接口

    分页搜索评价列表，支持关键词和状态筛选

    原始API路径: /client/evaluationListSearch
    """
    try:
        # 调用服务层搜索评价列表
        from module_admin.service.evaluation_service import EvaluationService
        result = await EvaluationService.evaluation_list_search_service(query_db, page, size, keyword, status)

        return ResponseUtil.success(
            msg="搜索评价列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询评价列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"搜索评价列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"搜索评价列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/evaluationDetail', summary="获取评价详情")
async def evaluation_detail(
    request: Request,
    evaluation_id: str = Query(..., description="评价ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价详情接口

    获取指定评价的详细信息

    原始API路径: /client/evaluationDetail
    """
    try:
        # 调用服务层获取评价详情
        from module_admin.service.evaluation_service import EvaluationService
        result = await EvaluationService.evaluation_detail_service(query_db, evaluation_id)

        return ResponseUtil.success(
            msg="获取评价详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"评价资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询评价详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/replyEvaluation', summary="回复评价")
async def reply_evaluation(
    request: Request,
    evaluation_id: str = Form(..., description="评价ID"),
    reply_content: str = Form(..., description="回复内容"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """回复评价接口

    回复指定评价

    原始API路径: /client/replyEvaluation
    """
    try:
        # 调用服务层回复评价
        from module_admin.service.evaluation_service import EvaluationService
        result = await EvaluationService.reply_evaluation_service(query_db, evaluation_id, reply_content)

        return ResponseUtil.success(
            msg="回复评价成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"评价资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"回复内容验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"回复评价业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"回复评价异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/evaluationIndicators', summary="获取评价指标列表")
async def evaluation_indicators(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价指标列表接口

    获取所有评价指标

    原始API路径: /client/evaluationIndicators
    """
    try:
        # 调用服务层获取评价指标列表
        from module_admin.service.evaluation_service import EvaluationService
        result = await EvaluationService.evaluation_indicators_service(query_db)

        return ResponseUtil.success(
            msg="获取评价指标列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询评价指标列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价指标列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价指标列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加门店相关接口
@order_controller.get('/storeInfo', summary="获取门店信息")
async def store_info(
    request: Request,
    company_id: Optional[str] = Query(None, description="公司ID"),
    store_uuid: Optional[str] = Query(None, description="门店UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user_or_staff)
):
    """获取门店信息接口

    获取当前门店的基本信息

    原始API路径: /client/storeInfo
    """
    try:
        # 添加调试日志
        logger.info(f"门店信息接口被调用，current_user类型: {type(current_user)}")
        if hasattr(current_user, 'user'):
            logger.info(f"current_user.user类型: {type(current_user.user)}")
            if hasattr(current_user.user, 'real_name'):
                logger.info(f"员工姓名: {current_user.user.real_name}")
            if hasattr(current_user.user, 'name'):
                logger.info(f"门店用户姓名: {current_user.user.name}")

        # 调用服务层获取门店信息，传入当前用户信息和可选的公司参数
        from module_admin.service.store_service import StoreService
        result = await StoreService.store_info_service(query_db, current_user, company_id, store_uuid)

        return ResponseUtil.success(
            msg="获取门店信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询门店信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取门店信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取门店信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/updateStoreInfo', summary="更新门店信息")
async def update_store_info(
    request: Request,
    store_data: Dict = Body(..., description="门店数据"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user_or_staff)
):
    """更新门店信息接口

    更新当前门店的基本信息

    原始API路径: /client/updateStoreInfo
    """
    try:
        # 调用服务层更新门店信息
        from module_admin.service.store_service import StoreService
        result = await StoreService.update_store_info_service(query_db, store_data)

        return ResponseUtil.success(
            msg="更新门店信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"门店数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新门店信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新门店信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/storeSchedule', summary="获取门店排班信息")
async def store_schedule(
    request: Request,
    date: Optional[str] = Query(None, description="日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取门店排班信息接口

    获取门店的排班信息，支持按日期筛选

    原始API路径: /client/storeSchedule
    """
    try:
        # 调用服务层获取门店排班信息
        from module_admin.service.store_service import StoreService
        result = await StoreService.store_schedule_service(query_db, date)

        return ResponseUtil.success(
            msg="获取门店排班信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询门店排班信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取门店排班信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取门店排班信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/updateStoreSchedule', summary="更新门店排班信息")
async def update_store_schedule(
    request: Request,
    schedule_data: Dict = Body(..., description="排班数据"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新门店排班信息接口

    更新门店的排班信息

    原始API路径: /client/updateStoreSchedule
    """
    try:
        # 调用服务层更新门店排班信息
        from module_admin.service.store_service import StoreService
        result = await StoreService.update_store_schedule_service(query_db, schedule_data)

        return ResponseUtil.success(
            msg="更新门店排班信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"排班数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新门店排班信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新门店排班信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/cityList', summary="获取城市列表")
async def city_list(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取城市列表接口

    获取所有城市列表

    原始API路径: /client/cityList
    """
    try:
        # 调用服务层获取城市列表
        from module_admin.service.store_service import StoreService
        result = await StoreService.city_list_service(query_db)

        return ResponseUtil.success(
            msg="获取城市列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询城市列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取城市列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取城市列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加统计相关接口
@order_controller.get('/dashboardData', summary="获取仪表盘数据")
async def dashboard_data(
    request: Request,
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取仪表盘数据接口

    获取仪表盘统计数据，支持日期范围筛选

    原始API路径: /client/dashboardData
    """
    try:
        # 调用服务层获取仪表盘数据
        from module_admin.service.statistics_service import StatisticsService
        result = await StatisticsService.dashboard_data_service(query_db, start_date, end_date)

        return ResponseUtil.success(
            msg="获取仪表盘数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询仪表盘数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取仪表盘数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取仪表盘数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/orderStatistics', summary="获取订单统计数据")
async def order_statistics(
    request: Request,
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取订单统计数据接口

    获取订单统计数据，支持日期范围筛选

    原始API路径: /client/orderStatistics
    """
    try:
        # 调用服务层获取订单统计数据
        from module_admin.service.statistics_service import StatisticsService
        result = await StatisticsService.order_statistics_service(query_db, start_date, end_date)

        return ResponseUtil.success(
            msg="获取订单统计数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询订单统计数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取订单统计数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取订单统计数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/staffStatistics', summary="获取员工统计数据")
async def staff_statistics(
    request: Request,
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取员工统计数据接口

    获取员工统计数据，支持日期范围筛选

    原始API路径: /client/staffStatistics
    """
    try:
        # 调用服务层获取员工统计数据
        from module_admin.service.statistics_service import StatisticsService
        result = await StatisticsService.staff_statistics_service(query_db, start_date, end_date)

        return ResponseUtil.success(
            msg="获取员工统计数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询员工统计数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取员工统计数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取员工统计数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/customerStatistics', summary="获取客户统计数据")
async def customer_statistics(
    request: Request,
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取客户统计数据接口

    获取客户统计数据，支持日期范围筛选

    原始API路径: /client/customerStatistics
    """
    try:
        # 调用服务层获取客户统计数据
        from module_admin.service.statistics_service import StatisticsService
        result = await StatisticsService.customer_statistics_service(query_db, start_date, end_date)

        return ResponseUtil.success(
            msg="获取客户统计数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询客户统计数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户统计数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户统计数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加订单V2接口
@order_controller.post('/saveOrder', summary="保存订单信息")
async def save_order(
    request: Request,
    order_data: Dict = Body(..., description="订单数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存订单信息接口

    创建或更新订单信息

    原始API路径: /orderV2/saveOrder
    """
    try:
        # 调用服务层保存订单信息
        result = await OrderService.save_order_service(query_db, order_data)

        return ResponseUtil.success(
            msg="保存订单信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"订单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存订单信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存订单信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/getOrderDetail', summary="获取订单详情")
async def get_order_detail(
    request: Request,
    order_id: str = Query(..., description="订单ID"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取订单详情接口

    获取指定订单的详细信息

    原始API路径: /orderV2/getOrderDetail
    """
    try:
        # 调用服务层获取订单详情
        result = await OrderService.get_order_detail_service(query_db, order_id)

        return ResponseUtil.success(
            msg="获取订单详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询订单详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取订单详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取订单详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/getOrderList', summary="获取订单列表")
async def get_order_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="订单状态"),
    keyword: Optional[str] = Form(None, description="关键词"),
    product_type: Optional[str] = Form(None, description="产品类型"),
    order_new: Optional[str] = Form(None, description="订单新旧状态"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取订单列表接口

    分页获取订单列表，支持多种筛选条件

    原始API路径: /orderV2/getOrderList
    """
    try:
        # 调用服务层获取订单列表
        result = await OrderService.get_order_list_service(query_db, page, size, status, keyword, product_type, order_new)

        return ResponseUtil.success(
            msg="获取订单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/getServeOrderList', summary="获取服务订单列表")
async def get_serve_order_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="订单状态"),
    keyword: Optional[str] = Form(None, description="关键词"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取服务订单列表接口

    分页获取服务订单列表，支持状态和关键词筛选

    原始API路径: /orderV2/getServeOrderList
    """
    try:
        # 调用服务层获取服务订单列表
        result = await OrderService.get_serve_order_list_service(query_db, page, size, status, keyword)

        return ResponseUtil.success(
            msg="获取服务订单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询服务订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取服务订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取服务订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/getServeOrderStat', summary="获取服务订单统计信息")
async def get_serve_order_stat(
    request: Request,
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取服务订单统计信息接口

    获取服务订单统计信息，支持日期范围筛选

    原始API路径: /orderV2/getServeOrderStat
    """
    try:
        # 调用服务层获取服务订单统计信息
        result = await OrderService.get_serve_order_stat_service(query_db, start_date, end_date)

        return ResponseUtil.success(
            msg="获取服务订单统计信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询服务订单统计信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取服务订单统计信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取服务订单统计信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/getOrderCardList', summary="获取订单卡片列表")
async def get_order_card_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="订单状态"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取订单卡片列表接口

    分页获取订单卡片列表，支持状态筛选

    原始API路径: /orderV2/getOrderCardList
    """
    try:
        # 调用服务层获取订单卡片列表
        result = await OrderService.get_order_card_list_service(query_db, page, size, status)

        return ResponseUtil.success(
            msg="获取订单卡片列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询订单卡片列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取订单卡片列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取订单卡片列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/updateOrderStatus', summary="更新订单状态")
async def update_order_status(
    request: Request,
    order_id: str = Form(..., description="订单ID"),
    status: int = Form(..., description="订单状态"),
    status_name: str = Form(..., description="订单状态名称"),
    query_db: AsyncSession = Depends(get_db)
):
    """更新订单状态接口

    更新指定订单的状态

    原始API路径: /orderV2/updateOrderStatus
    """
    try:
        # 调用服务层更新订单状态
        result = await OrderService.update_order_status_service(query_db, order_id, status, status_name)

        return ResponseUtil.success(
            msg="更新订单状态成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"订单状态数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"更新订单状态业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"更新订单状态异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/cancelOrder', summary="取消订单")
async def cancel_order(
    order_number: str = Form(..., description="订单编号"),
    query_db: AsyncSession = Depends(get_db)
):
    """取消订单接口

    将指定订单的状态修改为99（已取消）

    :param order_number: 订单编号
    :return: 取消结果
    """
    try:
        # 调用服务层取消订单
        result = await OrderService.cancel_order_service(query_db, order_number)

        return ResponseUtil.success(
            msg="订单取消成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"订单资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"取消订单数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"取消订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"取消订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 添加派单页面专用接口
@order_controller.post('/dispatchOrderList', summary="获取派单页面订单列表")
async def dispatch_order_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    status: Optional[str] = Form(None, description="订单状态"),
    keyword: Optional[str] = Form(None, description="搜索关键词"),
    order_type: Optional[str] = Form(None, description="订单类型"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取派单页面订单列表接口

    根据当前用户的store_uuid自动筛选订单，支持搜索、筛选、分页
    """
    try:
        # 获取当前用户的store_uuid
        if not current_user or not current_user.user or not current_user.user.store_uuid:
            return ResponseUtil.business_error(msg="用户未关联门店，无法查看订单")

        store_uuid = current_user.user.store_uuid

        # 调用服务层获取订单列表
        result = await OrderService.dispatch_order_list_service(
            query_db, page, size, status, keyword, order_type, store_uuid
        )

        return ResponseUtil.success(
            msg="获取派单订单列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询派单订单列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取派单订单列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取派单订单列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/dispatchOrderStats', summary="获取派单页面订单统计")
async def dispatch_order_stats(
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取派单页面订单统计接口

    根据当前用户的store_uuid获取订单状态统计
    """
    try:
        # 获取当前用户的store_uuid
        if not current_user or not current_user.user or not current_user.user.store_uuid:
            return ResponseUtil.business_error(msg="用户未关联门店，无法查看统计")

        store_uuid = current_user.user.store_uuid

        # 调用服务层获取订单统计
        result = await OrderService.dispatch_order_stats_service(query_db, store_uuid)

        return ResponseUtil.success(
            msg="获取派单订单统计成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询派单订单统计失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取派单订单统计业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取派单订单统计异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.get('/getDispatchableStaff', summary="获取可派单人员列表")
async def get_dispatchable_staff(
    order_number: str = Query(..., description="订单编号"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取可派单人员列表接口

    根据订单号获取该订单产品对应的可派单服务人员列表
    """
    try:
        # 调用服务层获取可派单人员列表
        result = await OrderService.get_dispatchable_staff_service(query_db, order_number)

        return ResponseUtil.success(
            msg="获取可派单人员列表成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取可派单人员数据验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"获取可派单人员资源不存在: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询可派单人员失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取可派单人员业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取可派单人员异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@order_controller.post('/assignStaff', summary="派单确认")
async def assign_staff(
    order_number: str = Form(..., description="订单编号"),
    staff_uuid: str = Form(..., description="服务人员UUID"),
    commission_amount: str = Form("0", description="提成金额"),
    service_reminder: str = Form("", description="服务提醒"),
    notify_staff: bool = Form(False, description="是否通知服务人员"),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """派单确认接口

    将指定的服务人员分配给订单，支持设置提成金额和服务提醒
    """
    try:
        # 调用服务层进行派单确认
        result = await OrderService.assign_staff_service(
            query_db, order_number, staff_uuid, commission_amount, service_reminder, notify_staff
        )

        return ResponseUtil.success(
            msg="派单成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"派单数据验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"派单资源不存在: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"派单查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"派单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"派单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@order_controller.post('/proxy-create', summary="代客下单")
async def create_proxy_order(
    request: Request,
    order_data: ProxyOrderCreateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user_or_staff)
):
    """
    代客下单接口

    创建一个新的代客下单订单，包含完整的客户信息、地址信息、产品信息等。
    支持门店端和员工端调用。
    """
    try:
        # 记录接收到的数据用于调试
        logger.info(f"代客下单接收到的数据: {order_data}")

        # 调用服务层创建代客下单
        result = await OrderService.create_proxy_order_service(query_db, order_data, current_user)

        # 代客下单成功后发送微信推送通知给门店长
        try:
            from module_admin.service.wechat_official_service import WechatOfficialService

            # 构建推送数据 - 推送给门店长
            push_result = await WechatOfficialService.send_order_created_notification(
                db=query_db,
                store_uuid=current_user.user.store_uuid,  # 使用门店UUID，推送给门店长
                order_number=result.get('order_number'),
                service_name=result.get('product_name', '家政服务'),  # 使用实际产品名称
                service_time=f"{order_data.order_info.service_date} {order_data.order_info.service_time}",
                order_status="已接单"
            )

            if push_result.get('success'):
                logger.info(f"代客下单微信推送成功（推送给门店长） - 订单号: {result.get('order_number')}, 门店UUID: {current_user.user.store_uuid}")
            else:
                logger.warning(f"代客下单微信推送失败（推送给门店长） - 订单号: {result.get('order_number')}, 错误: {push_result.get('message')}")

        except Exception as e:
            # 推送失败不影响订单创建成功
            logger.error(f"代客下单微信推送异常（推送给门店长） - 订单号: {result.get('order_number')}, 错误: {str(e)}")

        return ResponseUtil.success(
            data=result,
            msg="订单创建成功"
        )

    except ValidationException as e:
        logger.error(f"代客下单参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=1001)
    except BusinessException as e:
        logger.error(f"代客下单业务处理失败: {e.message}")
        # 根据业务异常类型返回相应的错误码
        error_code = getattr(e, 'code', 1010)
        if error_code == 1010:
            error_code = 1010  # 系统繁忙
        return ResponseUtil.business_error(msg=e.message, code=error_code)
    except ValueError as e:
        # Pydantic 验证错误
        logger.error(f"代客下单数据格式验证失败: {str(e)}")
        return ResponseUtil.validation_error(msg=f"数据格式错误: {str(e)}", code=422)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"代客下单异常: {error_info['message']}")
        logger.error(f"异常详情: {str(e)}")
        return ResponseUtil.error(msg=f"系统异常: {error_info['message']}", code=500)


@order_controller.post('/manual-balance-payment', summary="手动余额扣费")
async def manual_balance_payment(
    request: Request,
    payment_data: Dict = Body(..., description="支付数据"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    手动余额扣费接口

    对待付款状态的订单进行余额扣费操作
    """
    try:
        # 获取订单编号
        order_number = payment_data.get('order_number')
        if not order_number:
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层进行手动余额扣费
        result = await OrderService.manual_balance_payment_service(query_db, order_number, current_user)

        return ResponseUtil.success(
            msg="余额扣费成功",
            data=result
        )
    except Exception as e:
        logger.error(f"手动余额扣费异常: {str(e)}")
        return ResponseUtil.error(msg=f"余额扣费失败: {str(e)}", code=500)


@order_controller.post('/cash-payment', summary="现金支付")
async def cash_payment(
    request: Request,
    payment_data: Dict = Body(..., description="支付数据"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    现金支付接口

    验证客户余额并记录现金支付流水，不实际扣除余额
    """
    try:
        # 获取订单编号
        order_number = payment_data.get('order_number')
        if not order_number:
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层进行现金支付处理
        result = await OrderService.cash_payment_service(query_db, order_number, current_user)

        return ResponseUtil.success(
            msg="现金支付成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"现金支付参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=1001)
    except BusinessException as e:
        logger.error(f"现金支付业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=getattr(e, 'code', 1010))
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"现金支付异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@order_controller.post('/balance-payment', summary="余额支付")
async def balance_payment(
    request: Request,
    payment_data: Dict = Body(..., description="支付数据"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    余额支付接口

    验证客户余额并实际扣除余额，记录支付流水
    """
    try:
        # 获取订单编号
        order_number = payment_data.get('order_number')
        if not order_number:
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层进行余额支付处理
        result = await OrderService.balance_payment_service(query_db, order_number, current_user)

        return ResponseUtil.success(
            msg="余额支付成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"余额支付参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=1001)
    except BusinessException as e:
        logger.error(f"余额支付业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=getattr(e, 'code', 1010))
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"余额支付异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@order_controller.post('/qrcode-payment', summary="订单扫码支付")
async def order_qrcode_payment(
    request: Request,
    payment_data: Dict = Body(..., description="支付数据"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    订单扫码支付接口

    创建订单的扫码支付订单，返回支付二维码URL
    """
    try:
        # 获取订单编号
        order_number = payment_data.get('order_number')
        if not order_number:
            raise ValidationException(message="订单编号不能为空")

        # 调用服务层进行订单扫码支付处理
        result = await OrderService.order_qrcode_payment_service(query_db, order_number, current_user, request)

        return ResponseUtil.success(
            msg="创建扫码支付订单成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"订单扫码支付参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=1001)
    except BusinessException as e:
        logger.error(f"订单扫码支付业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=getattr(e, 'code', 1010))
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"订单扫码支付异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


# ==================== 订单修改接口 ====================

@order_controller.post('/updateOrderAmount', summary="修改订单金额")
async def update_order_amount(
    request: Request,
    update_data: UpdateOrderAmountRequest,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """修改订单金额接口

    商家端订单改价功能，只有订单状态小于50的订单才能修改金额
    修改order表中的total_pay_actual字段

    :param update_data: 订单改价请求数据
    :return: 修改结果
    """
    try:
        logger.info(f"商家修改订单金额请求，用户: {current_user.user.name}, 订单号: {update_data.order_number}, 新金额: {update_data.new_amount}")

        # 调用服务层修改订单金额
        result = await OrderService.update_order_amount_service(
            query_db,
            update_data.order_number,
            update_data.new_amount,
            current_user.user.store_uuid
        )

        logger.info(f"订单金额修改成功，订单号: {update_data.order_number}")
        return ResponseUtil.success(
            msg="订单金额修改成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"订单金额修改验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"订单金额修改查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"订单金额修改业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"订单金额修改异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'])

@order_controller.post('/updateOrderTime', summary="修改订单时间")
async def update_order_time(
    request: Request,
    update_data: UpdateOrderTimeRequest,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """修改订单时间接口

    商家端订单改时功能，只有订单状态小于50的订单才能修改时间
    修改order表中的service_date字段

    :param update_data: 订单改时请求数据
    :return: 修改结果
    """
    try:
        logger.info(f"商家修改订单时间请求，用户: {current_user.user.name}, 订单号: {update_data.order_number}, 新时间: {update_data.new_service_date}")

        # 调用服务层修改订单时间
        result = await OrderService.update_order_time_service(
            query_db,
            update_data.order_number,
            update_data.new_service_date,
            current_user.user.store_uuid
        )

        logger.info(f"订单时间修改成功，订单号: {update_data.order_number}")
        return ResponseUtil.success(
            msg="订单时间修改成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"订单时间修改验证异常: {e.message}")
        return ResponseUtil.failure(msg=e.message)
    except QueryException as e:
        logger.error(f"订单时间修改查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"订单时间修改业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"订单时间修改异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'])


# 订单分享详情接口已移动到 public_controller.py 中，路径为 /api/v1/public/order/share-detail


# 检查员工是否存在接口已移动到 public_controller.py 中，路径为 /api/v1/public/order/check-staff-exists


# 复制员工到指定门店接口已移动到 public_controller.py 中，路径为 /api/v1/public/order/copy-staff-to-store


# 通过分享接单接口已移动到 public_controller.py 中，路径为 /api/v1/public/order/accept-by-share