from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.help_service import HelpService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException, DatabaseException

# 使用统一的API路由前缀格式
help_controller = APIRouter(prefix='/api/v1/help', dependencies=[Depends(AuthAdapter.get_current_user)])

@help_controller.get('/getCategoryList', summary="获取帮助分类列表")
async def get_category_list(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取帮助分类列表接口
    
    获取所有帮助分类列表
    
    原始API路径: /helpv3/getCategoryList
    """
    try:
        # 调用服务层获取帮助分类列表
        result = await HelpService.get_help_category_list_service(query_db)
        
        return ResponseUtil.success(
            msg="获取帮助分类列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询帮助分类列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取帮助分类列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取帮助分类列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@help_controller.get('/getArticleList', summary="获取帮助文章列表")
async def get_article_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    keyword: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取帮助文章列表接口
    
    分页获取帮助文章列表，支持按分类ID和关键词筛选
    
    原始API路径: /helpv3/getArticleList
    """
    try:
        # 调用服务层获取帮助文章列表
        result = await HelpService.get_help_article_list_service(query_db, page, size, category_id, keyword)
        
        return ResponseUtil.success(
            msg="获取帮助文章列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询帮助文章列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取帮助文章列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取帮助文章列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@help_controller.get('/getArticleOne', summary="获取单个帮助文章信息")
async def get_article_one(
    request: Request,
    id: int = Query(..., description="文章ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取单个帮助文章信息接口
    
    根据ID获取帮助文章的详细信息
    
    原始API路径: /helpv3/getArticleOne
    """
    try:
        # 调用服务层获取单个帮助文章信息
        result = await HelpService.get_help_article_one_service(query_db, id)
        
        return ResponseUtil.success(
            msg="获取单个帮助文章信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取单个帮助文章信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"帮助文章不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询单个帮助文章信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取单个帮助文章信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取单个帮助文章信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@help_controller.post('/saveCategory', summary="保存帮助分类信息")
async def save_category(
    request: Request,
    id: Optional[int] = Form(None, description="分类ID"),
    name: str = Form(..., description="分类名称"),
    sort: int = Form(0, description="排序"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存帮助分类信息接口
    
    保存帮助分类信息
    
    原始API路径: /helpv3/saveCategory
    """
    try:
        # 构建分类数据
        category_data = {
            "id": id,
            "name": name,
            "sort": sort
        }
        
        # 调用服务层保存帮助分类信息
        result = await HelpService.save_help_category_service(query_db, category_data)
        
        return ResponseUtil.success(
            msg="保存帮助分类信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存帮助分类信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存帮助分类信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存帮助分类信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@help_controller.post('/saveArticle', summary="保存帮助文章信息")
async def save_article(
    request: Request,
    id: Optional[int] = Form(None, description="文章ID"),
    title: str = Form(..., description="标题"),
    content: str = Form(..., description="内容"),
    category_id: int = Form(..., description="分类ID"),
    sort: int = Form(0, description="排序"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存帮助文章信息接口
    
    保存帮助文章信息
    
    原始API路径: /helpv3/saveArticle
    """
    try:
        # 构建文章数据
        article_data = {
            "id": id,
            "title": title,
            "content": content,
            "category_id": category_id,
            "sort": sort
        }
        
        # 调用服务层保存帮助文章信息
        result = await HelpService.save_help_article_service(query_db, article_data)
        
        return ResponseUtil.success(
            msg="保存帮助文章信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存帮助文章信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存帮助文章信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存帮助文章信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@help_controller.post('/deleteCategory', summary="删除帮助分类")
async def delete_category(
    request: Request,
    id: int = Form(..., description="分类ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除帮助分类接口
    
    根据ID删除帮助分类
    
    原始API路径: /helpv3/deleteCategory
    """
    try:
        # 调用服务层删除帮助分类
        result = await HelpService.delete_help_category_service(query_db, id)
        
        return ResponseUtil.success(
            msg="删除帮助分类成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"删除帮助分类数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"帮助分类不存在或删除失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except DatabaseException as e:
        logger.error(f"删除帮助分类数据库异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除帮助分类业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除帮助分类异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@help_controller.post('/deleteArticle', summary="删除帮助文章")
async def delete_article(
    request: Request,
    id: int = Form(..., description="文章ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """删除帮助文章接口
    
    根据ID删除帮助文章
    
    原始API路径: /helpv3/deleteArticle
    """
    try:
        # 调用服务层删除帮助文章
        result = await HelpService.delete_help_article_service(query_db, id)
        
        return ResponseUtil.success(
            msg="删除帮助文章成功",
            data={"success": result}
        )
    except ValidationException as e:
        logger.error(f"删除帮助文章数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"帮助文章不存在或删除失败: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"删除帮助文章业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"删除帮助文章异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
