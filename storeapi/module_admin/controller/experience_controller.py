from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Body, Request, Depends
from module_admin.service.experience_service import ExperienceService
from module_admin.entity.vo.experience_vo import ExperienceRegisterRequest
from module_admin.entity.vo.common_vo import CrudResponseModel
from exceptions.exception import ValidationException, BusinessException
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from utils.message_util import send_sms_verification_code
from config.get_db import get_db


# 使用统一的API路由前缀格式
experience_controller = APIRouter(
    prefix="/api/v1/experience",
    tags=["experience"],
    dependencies=[],  # 清空默认依赖
    include_in_schema=True
)


@experience_controller.post('/register', response_model=CrudResponseModel, summary="体验注册")
async def register(
    request: Request,
    register_data: ExperienceRegisterRequest = Body(..., description="注册数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """体验注册接口

    接收前端传来的注册信息，验证短信验证码，并将数据保存到数据库

    Args:
        request: Request对象
        register_data: 注册数据
        query_db: 数据库会话

    Returns:
        注册结果
    """
    try:
        # 调用服务层进行注册
        result = await ExperienceService.register_service(request, query_db, register_data)

        return ResponseUtil.success(
            msg=result.message,
            data=result.result  # 使用result属性而不是data属性
        )
    except ValidationException as e:
        logger.error(f"体验注册验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"体验注册业务失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"体验注册异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@experience_controller.get('/getverifycode', response_model=CrudResponseModel, summary="获取短信验证码")
async def get_sms_code(
    request: Request,
    mobile: str
):
    """获取短信验证码接口

    发送短信验证码到指定手机号

    Args:
        request: Request对象
        mobile: 手机号

    Returns:
        发送结果
    """
    try:
        # 不再使用session_id，直接使用手机号作为Redis的键
        # 为了兼容现有接口，传入一个空字符串作为session_id
        result = await send_sms_verification_code(request, mobile, "")

        if result.get("success"):
            return ResponseUtil.success(
                msg="短信验证码发送成功",
                data={
                    "message": result.get("message", "验证码发送成功")
                }
            )
        else:
            return ResponseUtil.failure(
                msg=result.get("message", "验证码发送失败"),
                data={}
            )
    except BusinessException as e:
        logger.error(f"获取短信验证码失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取短信验证码异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


