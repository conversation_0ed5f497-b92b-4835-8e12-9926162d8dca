from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.evaluation_service import EvaluationService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
evaluation_controller = APIRouter(prefix='/api/v1/evaluation', dependencies=[Depends(AuthAdapter.get_current_user)])

@evaluation_controller.get('/post/getQuestion', summary="获取问题列表")
async def get_question(
    request: Request,
    type: str = Query(..., description="问题类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取问题列表接口
    
    根据问题类型获取评价问题列表
    
    原始API路径: /evaluationv3/post/getQuestion
    """
    try:
        # 调用服务层获取问题列表
        result = await EvaluationService.get_question_service(query_db, type)
        
        return ResponseUtil.success(
            msg="获取问题列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询问题列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取问题列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取问题列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@evaluation_controller.get('/post/getList', summary="获取评价列表")
async def get_post_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    type: Optional[str] = Query(None, description="评价类型"),
    status: Optional[str] = Query(None, description="状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价列表接口
    
    分页获取评价列表，支持按类型和状态筛选
    
    原始API路径: /evaluationv3/post/getList
    """
    try:
        # 调用服务层获取评价列表
        result = await EvaluationService.get_post_list_service(query_db, page, size, type, status)
        
        return ResponseUtil.success(
            msg="获取评价列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询评价列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@evaluation_controller.get('/record/getList', summary="获取评价记录列表")
async def get_record_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    post_uuid: Optional[str] = Query(None, description="评价UUID"),
    aunt_uuid: Optional[str] = Query(None, description="阿姨UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价记录列表接口
    
    分页获取评价记录列表，支持按评价UUID和阿姨UUID筛选
    
    原始API路径: /evaluationv3/record/getList
    """
    try:
        # 调用服务层获取评价记录列表
        result = await EvaluationService.get_record_list_service(query_db, page, size, post_uuid, aunt_uuid)
        
        return ResponseUtil.success(
            msg="获取评价记录列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询评价记录列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价记录列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价记录列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@evaluation_controller.get('/record/getSummary', summary="获取评价记录摘要")
async def get_record_summary(
    request: Request,
    post_uuid: str = Query(..., description="评价UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价记录摘要接口
    
    根据评价UUID获取评价记录的统计摘要信息
    
    原始API路径: /evaluationv3/record/getSummary
    """
    try:
        # 调用服务层获取评价记录摘要
        result = await EvaluationService.get_record_summary_service(query_db, post_uuid)
        
        return ResponseUtil.success(
            msg="获取评价记录摘要成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取评价记录摘要数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"评价资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询评价记录摘要失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价记录摘要业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价记录摘要异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@evaluation_controller.post('/post/saveInvite', summary="保存邀请信息")
async def save_invite(
    request: Request,
    post_uuid: str = Form(..., description="评价UUID"),
    aunt_uuid: str = Form(..., description="阿姨UUID"),
    contract_uuid: Optional[str] = Form(None, description="合同UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """保存邀请信息接口
    
    保存评价邀请信息，生成邀请链接
    
    原始API路径: /evaluationv3/post/saveInvite
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)
        
        # 构建邀请数据
        invite_data = {
            "post_uuid": post_uuid,
            "aunt_uuid": aunt_uuid,
            "contract_uuid": contract_uuid,
            "creator_uuid": current_user.get("user_uuid")
        }
        
        # 调用服务层保存邀请信息
        result = await EvaluationService.save_invite_service(query_db, invite_data)
        
        return ResponseUtil.success(
            msg="保存邀请信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"保存邀请信息数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存邀请信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存邀请信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@evaluation_controller.post('/index/getList', summary="获取评价首页列表")
async def get_index_list(
    request: Request,
    page: int = Form(1, description="页码"),
    size: int = Form(20, description="每页数量"),
    type: Optional[str] = Form(None, description="评价类型"),
    keyword: Optional[str] = Form(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取评价首页列表接口
    
    分页获取评价首页列表，支持按类型和关键词筛选
    
    原始API路径: /evaluationv3/index/getList
    """
    try:
        # 调用服务层获取评价首页列表
        result = await EvaluationService.get_index_list_service(query_db, page, size, type, keyword)
        
        return ResponseUtil.success(
            msg="获取评价首页列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询评价首页列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取评价首页列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取评价首页列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
