from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.internal_user_vo import (
    CreateInternalUserRequest,
    UpdateInternalUserRequest,
    InternalUserResponse,
    InternalUserDetailResponse,
    InternalUserQuery
)
from module_admin.service.internal_user_service import InternalUserService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.auth_adapter import AuthAdapter
from utils.response_util import ResponseUtil


# 使用统一的API路由前缀格式
internalUserController = APIRouter(prefix='/api/v1/internal-user')


@internalUserController.post('/create', response_model=InternalUserResponse)
@Log(title='创建内部用户', business_type=BusinessType.INSERT)
async def create_internal_user(
    request: Request,
    user: CreateInternalUserRequest,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """创建内部用户"""
    result = await InternalUserService.create_internal_user(query_db, user)
    return ResponseUtil.success(msg='创建成功', model_content=result)


@internalUserController.put('/update', response_model=InternalUserResponse)
@Log(title='更新内部用户', business_type=BusinessType.UPDATE)
async def update_internal_user(
    request: Request,
    user: UpdateInternalUserRequest,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """更新内部用户"""
    result = await InternalUserService.update_internal_user(query_db, user)
    return ResponseUtil.success(msg='更新成功', model_content=result)


@internalUserController.delete('/delete/{user_id}', response_model=CrudResponseModel)
@Log(title='删除内部用户', business_type=BusinessType.DELETE)
async def delete_internal_user(
    request: Request,
    user_id: str,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """删除内部用户"""
    result = await InternalUserService.delete_internal_user(query_db, user_id)
    return ResponseUtil.success(msg='删除成功' if result else '删除失败')


@internalUserController.get('/get/{user_id}', response_model=InternalUserResponse)
@Log(title='获取内部用户详情', business_type=BusinessType.OTHER)
async def get_internal_user(
    request: Request,
    user_id: str,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取内部用户详情"""
    result = await InternalUserService.get_internal_user(query_db, user_id)
    return ResponseUtil.success(model_content=result)


@internalUserController.get('/roles')
@Log(title='获取可用角色列表', business_type=BusinessType.OTHER)
async def get_available_roles(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """获取可用角色列表"""
    result = await InternalUserService.get_available_roles(query_db)
    return ResponseUtil.success(data=result)


@internalUserController.get('/detail/{user_id}', response_model=InternalUserDetailResponse)
@Log(title='获取内部用户完整详情', business_type=BusinessType.OTHER)
async def get_internal_user_detail(
    request: Request,
    user_id: str,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取内部用户完整详情"""
    result = await InternalUserService.get_internal_user_detail(query_db, user_id)
    return ResponseUtil.success(data=result)


@internalUserController.get('/list')
@Log(title='获取内部用户列表', business_type=BusinessType.OTHER)
async def get_internal_users(
    request: Request,
    page: int = 1,
    page_size: int = 10,
    mobile: str = None,
    user_name: str = None,
    company_id: str = None,
    store_id: str = None,
    role_id: str = None,
    status: str = None,
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取内部用户列表"""
    # 自动使用当前登录用户的company_id进行筛选，除非明确指定了company_id
    if not company_id and current_user and current_user.user:
        company_id = current_user.user.company_id

    query = InternalUserQuery(
        page=page,
        page_size=page_size,
        mobile=mobile,
        name=user_name,
        company_id=company_id,
        store_id=store_id,
        role_id=role_id,
        status=status
    )
    result = await InternalUserService.get_internal_users(query_db, query)
    return ResponseUtil.success(data=result)


