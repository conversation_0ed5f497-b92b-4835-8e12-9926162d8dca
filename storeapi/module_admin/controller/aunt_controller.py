from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.get_db import get_db
from pydantic import BaseModel
from module_admin.service.aunt_service import AuntService
from module_admin.service.aunt_autonomous_service import AuntAutonomousService
from module_admin.service.aunt_invite_service import AuntInviteService
from module_admin.service.aunt_evaluate_service import AuntEvaluateService
from module_admin.service.aunt_bank_service import AuntBankService
from module_admin.service.aunt_id_check_service import AuntIdCheckService
from module_admin.service.aunt_schedule_service import AuntScheduleService
from module_admin.service.aunt_credit_service import AuntCreditService
from module_admin.service.credit_query_service import CreditQueryService
from module_admin.service.aunt_save_service import AuntSaveService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.entity.vo.aunt_save_vo import Aunt<PERSON>aveRequestModel
from module_admin.entity.vo.internal_user_vo import CurrentInternalUserModel
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 阿姨管理列表请求模型
class AuntManageListRequest(BaseModel):
    """阿姨管理列表请求参数"""
    page: int = 1
    limit: int = 16
    sort_field: str = ""
    sort_type: str = ""
    search: str = ""
    type: str = ""
    status: str = ""
    user_uuid: str = "0"
    age: str = ""
    store_uuid: str = ""
    minage: str = ""
    maxage: str = ""
    hometown: str = "0"
    hometown_city: str = ""
    province_id: str = ""
    city_id: str = ""
    area_id: str = ""
    can_live_home: str = ""
    aunt_skill: str = ""
    chinese_zodiac: str = ""
    zodiac: str = ""
    sex: str = "0"
    nation: str = ""
    education: str = ""
    marry: str = ""
    level: str = "0"
    salary: str = ""
    salary1: str = ""
    salary2: str = ""
    createTime: str = ""
    create_time: str = ""
    religion: str = ""
    importVisible: str = "false"
    insurance_status: str = ""
    search_type: str = ""
    update_time: str = ""
    updateTime: str = ""
    aunt_source: str = ""

# 使用统一的API路由前缀格式
aunt_controller = APIRouter(prefix='/api/v1/aunt', dependencies=[Depends(InternalUserLoginService.get_current_user)])

@aunt_controller.get('/findAuntList', summary="获取阿姨列表")
async def find_aunt_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    n_pe_report_tag: Optional[str] = Query(None, description="体检报告标签"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨列表接口

    根据条件查询阿姨列表，支持分页

    原始API路径: /aunt/findAuntList
    """
    try:
        # 调用服务层获取阿姨列表
        result = await AuntService.find_aunt_list_service(query_db, page, size, n_pe_report_tag)

        return ResponseUtil.success(
            msg="获取阿姨列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询阿姨列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/findAuntDetail', summary="获取阿姨详情")
async def find_aunt_detail(
    request: Request,
    uuid: str = Query(..., description="阿姨UUID"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨详情接口

    根据UUID获取阿姨的详细信息

    原始API路径: /aunt/findAuntDetail
    """
    try:
        # 调用服务层获取阿姨详情
        result = await AuntService.find_aunt_detail_service(query_db, uuid)

        return ResponseUtil.success(
            msg="获取阿姨详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"阿姨资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询阿姨详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/findAuntScheduleList', summary="获取阿姨排班列表")
async def find_aunt_schedule_list(
    request: Request,
    aunt_id: str = Query(..., description="阿姨ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨排班列表接口

    根据阿姨ID和日期范围获取阿姨的排班信息

    原始API路径: /aunt/findAuntScheduleList
    """
    try:
        # 调用服务层获取阿姨排班列表
        result = await AuntService.find_aunt_schedule_list_service(query_db, aunt_id, start_date, end_date)

        return ResponseUtil.success(
            msg="获取阿姨排班列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询阿姨排班列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨排班列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨排班列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 阿姨V2接口
@aunt_controller.post('/contract', summary="获取家政员合同信息")
async def aunt_contract(
    request: Request,
    aunt_id: str = Form(..., description="阿姨ID"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取家政员合同信息接口

    根据阿姨ID获取家政员的合同信息

    原始API路径: /auntv2/contract
    """
    try:
        # 调用服务层获取家政员合同信息
        result = await AuntService.aunt_contract_service(query_db, aunt_id)

        return ResponseUtil.success(
            msg="获取家政员合同信息成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"阿姨合同资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询阿姨合同信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨合同信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨合同信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

# 阿姨V3接口
@aunt_controller.get('/fissionResume/getList', summary="获取阿姨裂变简历列表")
async def get_fission_resume_list(
    request: Request,
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨裂变简历列表接口

    分页获取阿姨裂变简历列表

    原始API路径: /auntV3/fissionResume/getList
    """
    try:
        # 调用服务层获取阿姨裂变简历列表
        result = await AuntService.get_fission_resume_list_service(query_db, page, page_size)

        return ResponseUtil.success(
            msg="获取阿姨裂变简历列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询阿姨裂变简历列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨裂变简历列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨裂变简历列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/autonomous', summary="获取阿姨自主服务信息")
async def get_autonomous_info(
    request: Request,
    uid: str = Query(..., description="用户ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨自主服务信息接口

    根据用户ID获取阿姨自主服务信息

    原始API路径: /aunt/autonomous
    """
    try:
        # 调用服务层获取阿姨自主服务信息
        result = await AuntAutonomousService.get_autonomous_info_service(query_db, uid)

        return ResponseUtil.success(
            msg="获取阿姨自主服务信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询阿姨自主服务信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨自主服务信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨自主服务信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/autonomousList', summary="获取阿姨自主服务列表")
async def get_autonomous_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    scene: Optional[str] = Query(None, description="场景"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨自主服务列表接口

    分页获取阿姨自主服务列表

    原始API路径: /aunt/autonomousList
    """
    try:
        # 调用服务层获取阿姨自主服务列表
        result = await AuntAutonomousService.find_autonomous_list_service(query_db, page, size, scene)

        return ResponseUtil.success(
            msg="获取阿姨自主服务列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询阿姨自主服务列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨自主服务列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨自主服务列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/inviteAunt', summary="邀请阿姨")
async def invite_aunt(
    request: Request,
    type: Optional[str] = Query(None, description="类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """邀请阿姨接口

    获取邀请阿姨的相关信息

    原始API路径: /aunt/inviteAunt
    """
    try:
        # 调用服务层获取邀请阿姨信息
        result = await AuntInviteService.get_invite_aunt_service(query_db, type)

        return ResponseUtil.success(
            msg="获取邀请阿姨信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询邀请阿姨信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取邀请阿姨信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取邀请阿姨信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/inviteAuntList', summary="获取邀请阿姨列表")
async def invite_aunt_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取邀请阿姨列表接口

    分页获取邀请阿姨列表

    原始API路径: /aunt/inviteAuntList
    """
    try:
        # 调用服务层获取邀请阿姨列表
        result = await AuntInviteService.find_invite_aunt_list_service(query_db, page, size)

        return ResponseUtil.success(
            msg="获取邀请阿姨列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询邀请阿姨列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取邀请阿姨列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取邀请阿姨列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.post('/getAuntEvaluateUrl', summary="获取阿姨评价链接")
async def get_aunt_evaluate_url(
    request: Request,
    aunt_uuid: str = Form(..., description="阿姨UUID"),
    type: str = Form(..., description="评价类型"),
    contract_uuid: str = Form(..., description="合同UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨评价链接接口

    获取指定阿姨的评价链接，以便用户可以对阿姨的服务进行评价

    原始API路径: /aunt/getAuntEvaluateUrl
    """
    try:
        # 调用服务层获取阿姨评价链接
        result = await AuntEvaluateService.get_aunt_evaluate_url_service(query_db, aunt_uuid, type, contract_uuid)

        return ResponseUtil.success(
            msg="获取阿姨评价链接成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取阿姨评价链接参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询阿姨评价链接失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨评价链接业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨评价链接异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.post('/createAuntBank', summary="创建阿姨银行账户")
async def create_aunt_bank(
    request: Request,
    open_account: str = Form(..., description="开户地点"),
    name: str = Form(..., description="阿姨姓名"),
    bank_name: str = Form(..., description="银行名称"),
    aunt_uuid: str = Form(..., description="阿姨UUID"),
    account: str = Form(..., description="银行账号"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """创建阿姨银行账户接口

    为阿姨创建银行账户信息

    原始API路径: /aunt/createAuntBank
    """
    try:
        # 调用服务层创建阿姨银行账户
        result = await AuntBankService.create_aunt_bank_service(query_db, open_account, name, bank_name, aunt_uuid, account)

        return ResponseUtil.success(
            msg="创建阿姨银行账户成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"创建阿姨银行账户参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"创建阿姨银行账户失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建阿姨银行账户业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建阿姨银行账户异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/checkAuntIdNumber', summary="检查阿姨身份证号码")
async def check_aunt_id_number(
    request: Request,
    aunt_uuid: str = Query(..., description="阿姨UUID"),
    mt: Optional[str] = Query(None, description="模块类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """检查阿姨身份证号码接口

    验证阿姨的身份证号码是否有效，并返回相关的验证结果和积分信息

    原始API路径: /aunt/checkAuntIdNumber
    """
    try:
        # 调用服务层检查阿姨身份证号码
        result = await AuntIdCheckService.check_aunt_id_number_service(query_db, aunt_uuid, mt)

        return ResponseUtil.success(
            msg="检查阿姨身份证号码成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"检查阿姨身份证号码参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"检查阿姨身份证号码失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"检查阿姨身份证号码业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"检查阿姨身份证号码异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.post('/createAuntSchedule', summary="创建阿姨排班")
async def create_aunt_schedule(
    request: Request,
    aunt_uuid: str = Form(..., description="阿姨UUID"),
    start_date: str = Form(..., description="排班开始日期"),
    end_date: str = Form(..., description="排班结束日期"),
    customer_name: str = Form(..., description="客户姓名"),
    customer_phone: str = Form(..., description="客户电话号码"),
    service_type: str = Form("", description="服务类型"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """创建阿姨排班接口

    创建阿姨的排班信息

    原始API路径: /aunt/createAuntSchedule
    """
    try:
        # 调用服务层创建阿姨排班
        result = await AuntScheduleService.create_aunt_schedule_service(
            query_db,
            aunt_uuid,
            start_date,
            end_date,
            customer_name,
            customer_phone,
            service_type
        )

        return ResponseUtil.success(
            msg="创建阿姨排班成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"创建阿姨排班参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"创建阿姨排班失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建阿姨排班业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建阿姨排班异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.post('/checkAuntNumberCredit', summary="检查阿姨信用")
async def check_aunt_number_credit(
    request: Request,
    aunt_uuid: str = Form(..., description="阿姨UUID"),
    auth_type: str = Form("0", description="授权类型"),
    id_number: Optional[str] = Form(None, description="身份证号码"),
    name: Optional[str] = Form(None, description="姓名"),
    report_stuff_id: Optional[str] = Form(None, description="报告员工ID"),
    is_estimate: str = Form("0", description="是否估计"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """检查阿姨信用接口

    检查阿姨的信用信息，包括身份证验证、老赖查询、法院被执行人等

    原始API路径: /aunt/checkAuntNumberCredit
    """
    try:
        # 调用服务层检查阿姨信用
        result = await AuntCreditService.check_aunt_number_credit_service(
            query_db,
            aunt_uuid,
            auth_type,
            id_number,
            name,
            report_stuff_id,
            is_estimate
        )

        return ResponseUtil.success(
            msg="检查阿姨信用成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"检查阿姨信用参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"检查阿姨信用失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"检查阿姨信用业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"检查阿姨信用异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.get('/getAuntNumberCredit', summary="获取阿姨信用信息")
async def get_aunt_number_credit(
    request: Request,
    aunt_uuid: str = Query(..., description="阿姨UUID"),
    id_number: Optional[str] = Query(None, description="身份证号码"),
    name: Optional[str] = Query(None, description="姓名"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨信用信息接口

    获取阿姨的信用信息，包括身份证验证、老赖查询、法院被执行人等

    原始API路径: /aunt/getAuntNumberCredit
    """
    try:
        # 调用服务层获取阿姨信用信息
        result = await AuntCreditService.get_aunt_number_credit_service(
            query_db,
            aunt_uuid,
            id_number,
            name
        )

        return ResponseUtil.success(
            msg="获取阿姨信用信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取阿姨信用信息参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取阿姨信用信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨信用信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨信用信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@aunt_controller.post('/auntmanage/list', summary="获取阿姨管理列表")
async def get_aunt_manage_list(
    request: Request,
    aunt_request: AuntManageListRequest = Body(...),
    current_user = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取阿姨管理列表接口

    根据查询条件获取阿姨管理列表数据，支持分页和多条件筛选。
    参考sys项目的/prod-api/api/v1/aunts/auntmanage/list接口实现。
    自动根据当前用户的门店UUID进行数据过滤。
    """
    try:
        # 获取当前用户的门店UUID
        user_store_uuid = current_user.user.store_uuid if current_user.user else None

        # 如果请求参数中没有指定store_uuid，则使用当前用户的门店UUID
        if not aunt_request.store_uuid and user_store_uuid:
            aunt_request.store_uuid = user_store_uuid
            logger.info(f"自动设置门店过滤条件，门店UUID: {user_store_uuid}")

        # 调用服务层获取阿姨管理列表
        result = await AuntService.get_aunt_manage_list_service(query_db, aunt_request)

        return ResponseUtil.success(
            msg="获取阿姨管理列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询阿姨管理列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取阿姨管理列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取阿姨管理列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.post('/save', summary="保存阿姨简历")
async def save_aunt_resume(
    request: Request,
    save_data: AuntSaveRequestModel = Body(...),
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """保存阿姨简历接口

    用于保存阿姨的简历信息，包括基本信息和详细信息
    """
    try:
        # 获取当前用户的门店信息
        store_uuid = current_user.user.store_uuid if current_user.user else None
        user_id = current_user.user.id if current_user.user else 0

        logger.info(f"保存阿姨简历 - 用户ID: {user_id}, 门店UUID: {store_uuid}")

        # 调用服务层保存阿姨简历
        result = await AuntSaveService.save_aunt_resume(query_db, save_data, store_uuid, user_id)

        return ResponseUtil.success(
            msg="简历保存成功",
            data=result.model_dump()
        )
    except ValidationException as e:
        logger.error(f"保存阿姨简历参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"保存阿姨简历业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"保存阿姨简历异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


# ========== 极证云信用查询接口 ==========

@aunt_controller.post('/jizhengLitigationQuery', summary="极证云人员涉诉信息查询")
async def jizheng_litigation_query(
    request: Request,
    name: str = Form(..., description="姓名"),
    cert_no: str = Form(..., description="身份证号"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """极证云人员涉诉信息查询接口

    查询人员的涉诉信息，包括法院被执行人、失信被执行人等信息

    API路径: /aunt/jizhengLitigationQuery
    """
    try:
        # 调用服务层进行人员涉诉信息查询
        result = await AuntCreditService.litigation_query_service(
            query_db,
            name,
            cert_no
        )

        return ResponseUtil.success(
            msg="人员涉诉信息查询成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"人员涉诉信息查询参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"人员涉诉信息查询业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"人员涉诉信息查询异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.post('/jizhengSpecialListVerify', summary="极证云特殊名单验证")
async def jizheng_special_list_verify(
    request: Request,
    name: str = Form(..., description="姓名"),
    cert_no: str = Form(..., description="身份证号"),
    phone: str = Form(..., description="手机号"),
    linkman_cell: Optional[str] = Form(None, description="其他手机号（可选，多个用逗号分隔，最多3个）"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """极证云特殊名单验证接口

    验证人员是否在特殊名单中，包括黑名单、风险名单等

    API路径: /aunt/jizhengSpecialListVerify
    """
    try:
        # 调用服务层进行特殊名单验证
        result = await AuntCreditService.special_list_verify_service(
            query_db,
            name,
            cert_no,
            phone,
            linkman_cell
        )

        return ResponseUtil.success(
            msg="特殊名单验证成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"特殊名单验证参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"特殊名单验证业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"特殊名单验证异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.post('/jizhengPhotoIdCompare', summary="极证云照片人证比对")
async def jizheng_photo_id_compare(
    request: Request,
    name: str = Form(..., description="姓名"),
    cert_no: str = Form(..., description="身份证号"),
    attachment: Optional[str] = Form(None, description="图片的base64编码（去掉编码头，大小<=300kb）"),
    image_path: Optional[str] = Form(None, description="图片文件路径（与attachment二选一）"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """极证云照片人证比对接口

    进行照片与身份证的人证比对，验证照片中的人员与身份证信息是否一致

    API路径: /aunt/jizhengPhotoIdCompare
    """
    try:
        # 调用服务层进行照片人证比对
        result = await AuntCreditService.photo_id_compare_service(
            query_db,
            name,
            cert_no,
            image_path,
            attachment
        )

        return ResponseUtil.success(
            msg="照片人证比对成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"照片人证比对参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"照片人证比对业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"照片人证比对异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.post('/convertAvatarToBase64', summary="转换头像为base64")
async def convert_avatar_to_base64(
    request: Request,
    avatar_url: str = Form(..., description="头像URL"),
    current_user: dict = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """转换头像URL为base64编码

    API路径: /aunt/convertAvatarToBase64
    """
    try:
        # 调用服务层转换头像
        result = await AuntCreditService.convert_avatar_to_base64_service(
            query_db,
            avatar_url
        )

        return ResponseUtil.success(
            msg="头像转换成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"头像转换参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"头像转换业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"头像转换异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


# 信用查询缓存相关API
class CreditQueryRequest(BaseModel):
    """信用查询请求参数"""
    staff_type: int  # 员工类型：1-aunt表，2-service_staff表
    staff_id: int    # 员工ID
    staff_uuid: str  # 员工UUID
    staff_name: str  # 员工姓名
    id_number: str   # 身份证号
    name: str        # 姓名
    mobile: str      # 手机号
    image_path: Optional[str] = None      # 图片路径
    attachment: Optional[str] = None      # 图片base64编码
    store_uuid: Optional[str] = None      # 门店UUID
    store_name: Optional[str] = None      # 门店名称
    operator_id: Optional[str] = None     # 操作人ID
    operator_name: Optional[str] = None   # 操作人姓名
    force_refresh: bool = False           # 是否强制刷新


@aunt_controller.post('/getCachedCreditResult', summary="获取缓存的信用查询结果")
async def get_cached_credit_result(
    request: Request,
    staff_type: int = Form(..., description="员工类型：1-aunt表，2-service_staff表"),
    staff_id: int = Form(..., description="员工ID"),
    staff_uuid: str = Form(..., description="员工UUID"),
    id_number: str = Form(..., description="身份证号"),
    current_user: dict = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取缓存的信用查询结果接口
    
    先从数据库查询是否有该员工的历史信用查询记录，如果有则直接返回，无需重新查询
    
    API路径: /aunt/getCachedCreditResult
    """
    try:
        # 调用服务层获取缓存结果
        result = await CreditQueryService.get_cached_credit_result(
            query_db,
            staff_type,
            staff_id,
            staff_uuid,
            id_number
        )
        
        if result:
            return ResponseUtil.success(
                msg="获取缓存信用查询结果成功",
                data=result
            )
        else:
            return ResponseUtil.success(
                msg="未找到缓存记录",
                data=None
            )
            
    except ValidationException as e:
        logger.error(f"获取缓存信用查询结果参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取缓存信用查询结果业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取缓存信用查询结果异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.post('/performFullCreditQuery', summary="执行完整信用查询")
async def perform_full_credit_query(
    request: Request,
    staff_type: int = Form(..., description="员工类型：1-aunt表，2-service_staff表"),
    staff_id: int = Form(..., description="员工ID"),
    staff_uuid: str = Form(..., description="员工UUID"),
    staff_name: str = Form(..., description="员工姓名"),
    id_number: str = Form(..., description="身份证号"),
    name: str = Form(..., description="姓名"),
    mobile: str = Form(..., description="手机号"),
    image_path: Optional[str] = Form(None, description="图片路径"),
    attachment: Optional[str] = Form(None, description="图片base64编码"),
    store_uuid: Optional[str] = Form(None, description="门店UUID"),
    store_name: Optional[str] = Form(None, description="门店名称"),
    operator_id: Optional[str] = Form(None, description="操作人ID"),
    operator_name: Optional[str] = Form(None, description="操作人姓名"),
    force_refresh: bool = Form(False, description="是否强制刷新（重新查询）"),
    current_user: dict = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """执行完整信用查询接口
    
    执行完整的信用查询流程，包括人证比对、特殊名单验证、涉诉信息查询
    支持缓存机制：如果不是强制刷新，会先尝试返回缓存结果
    
    API路径: /aunt/performFullCreditQuery
    """
    try:
        # 调用服务层执行完整信用查询
        result = await CreditQueryService.perform_full_credit_query(
            query_db,
            staff_type,
            staff_id,
            staff_uuid,
            staff_name,
            id_number,
            name,
            mobile,
            image_path,
            attachment,
            store_uuid,
            store_name,
            operator_id,
            operator_name,
            force_refresh
        )
        
        return ResponseUtil.success(
            msg="信用查询成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"信用查询参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"信用查询业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"信用查询异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.get('/getCreditQueryHistory', summary="获取信用查询历史记录")
async def get_credit_query_history(
    request: Request,
    store_uuid: str = Query(..., description="门店UUID"),
    limit: int = Query(20, description="限制数量"),
    offset: int = Query(0, description="偏移量"),
    current_user: dict = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取信用查询历史记录接口
    
    获取指定门店的信用查询历史记录列表
    
    API路径: /aunt/getCreditQueryHistory
    """
    try:
        # 调用服务层获取查询历史
        result = await CreditQueryService.get_query_history(
            query_db,
            store_uuid,
            limit,
            offset
        )
        
        return ResponseUtil.success(
            msg="获取查询历史成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"获取查询历史参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取查询历史业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取查询历史异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@aunt_controller.post('/performCreditQueryByUuid', summary="通过UUID执行信用查询")
async def perform_credit_query_by_uuid(
    request: Request,
    staff_uuid: str = Form(..., description="员工UUID"),
    staff_type: Optional[int] = Form(None, description="员工类型：1-aunt表，2-service_staff表，不填自动判断"),
    image_path: Optional[str] = Form(None, description="图片路径"),
    attachment: Optional[str] = Form(None, description="图片base64编码"),
    operator_id: Optional[str] = Form(None, description="操作人ID"),
    operator_name: Optional[str] = Form(None, description="操作人姓名"),
    force_refresh: bool = Form(False, description="是否强制刷新（重新查询）"),
    current_user: dict = Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """通过员工UUID执行信用查询接口
    
    自动从数据库获取员工信息（姓名、身份证号、手机号），然后执行信用查询
    支持缓存机制：如果不是强制刷新，会先尝试返回缓存结果
    
    API路径: /aunt/performCreditQueryByUuid
    """
    try:
        # 调用服务层执行信用查询
        result = await CreditQueryService.perform_credit_query_by_uuid(
            query_db,
            staff_uuid,
            staff_type,
            image_path,
            attachment,
            operator_id,
            operator_name,
            force_refresh
        )
        
        return ResponseUtil.success(
            msg="信用查询成功",
            data=result
        )
        
    except ValidationException as e:
        logger.error(f"信用查询参数验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"信用查询业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"信用查询异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
