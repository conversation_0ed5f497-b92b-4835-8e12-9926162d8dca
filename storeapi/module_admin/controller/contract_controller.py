from fastapi import APIRouter, Depends, Request, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.get_db import get_db
from module_admin.service.contract_service import ContractService
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.entity.vo.contract_vo import ContractQueryModel, ContractCreateModel
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
contract_controller = APIRouter(prefix='/api/v1/contract', dependencies=[Depends(AuthAdapter.get_current_user)])

@contract_controller.get('/list', summary="获取合同列表")
async def get_contract_list(
    request: Request,
    store_uuid: str = Query(..., description="门店UUID"),
    page: int = Query(1, description="页码", ge=1),
    size: int = Query(10, description="每页数量", ge=1, le=100),
    keyword: Optional[str] = Query(None, description="搜索关键词（合同编号、客户姓名等）"),
    status: Optional[str] = Query(None, description="合同状态筛选"),
    contract_type: Optional[str] = Query(None, description="合同类型筛选"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取合同列表接口
    
    根据门店UUID获取合同列表，支持分页、搜索和筛选
    """
    try:
        # 构建查询参数
        query_params = ContractQueryModel(
            store_uuid=store_uuid,
            page=page,
            size=size,
            keyword=keyword,
            status=status,
            contract_type=contract_type
        )
        
        # 调用服务层获取合同列表
        result = await ContractService.get_contract_list_service(query_db, query_params)
        
        return ResponseUtil.success(
            msg="获取合同列表成功",
            data=result.model_dump()
        )
        
    except ValidationException as e:
        logger.error(f"合同列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询合同列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取合同列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取合同列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@contract_controller.get('/detail/{contract_id}', summary="获取合同详情")
async def get_contract_detail(
    request: Request,
    contract_id: int = Path(..., description="合同ID"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取合同详情接口
    
    根据合同ID获取合同的详细信息
    """
    try:
        # 调用服务层获取合同详情
        result = await ContractService.get_contract_detail_service(query_db, contract_id)
        
        return ResponseUtil.success(
            msg="获取合同详情成功",
            data=result
        )
        
    except ResourceNotFoundException as e:
        logger.error(f"合同资源不存在: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询合同详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取合同详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取合同详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@contract_controller.get('/statistics', summary="获取合同统计信息")
async def get_contract_statistics(
    request: Request,
    store_uuid: str = Query(..., description="门店UUID"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取合同统计信息接口
    
    根据门店UUID获取合同的统计信息
    """
    try:
        # 调用服务层获取统计信息
        result = await ContractService.get_contract_statistics_service(query_db, store_uuid)
        
        return ResponseUtil.success(
            msg="获取合同统计信息成功",
            data=result.model_dump()
        )
        
    except QueryException as e:
        logger.error(f"查询合同统计信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取合同统计信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取合同统计信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@contract_controller.post('/create', summary="创建合同")
async def create_contract(
    request: Request,
    contract_data: ContractCreateModel = Body(..., description="合同创建数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """创建合同接口

    创建新的服务合同
    """
    try:
        # 调用服务层创建合同
        result = await ContractService.create_contract_service(query_db, contract_data)

        return ResponseUtil.success(
            msg="合同创建成功",
            data=result
        )

    except ValidationException as e:
        # 验证异常，直接向上传递
        logger.error(f"创建合同验证异常: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        # 业务异常，直接向上传递
        logger.error(f"创建合同业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 其他异常，包装为业务异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建合同异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@contract_controller.get('/customer/{customer_uuid}', summary="根据客户UUID获取合同列表")
async def get_contracts_by_customer_uuid(
    request: Request,
    customer_uuid: str = Path(..., description="客户UUID"),
    query_db: AsyncSession = Depends(get_db)
):
    """根据客户UUID获取合同列表接口

    根据客户UUID获取该客户的所有合同记录
    """
    try:
        # 调用服务层获取合同列表
        result = await ContractService.get_contracts_by_customer_uuid_service(query_db, customer_uuid)

        return ResponseUtil.success(
            msg="获取客户合同列表成功",
            data=result
        )

    except QueryException as e:
        logger.error(f"获取客户合同列表查询异常: {e.message}")
        return ResponseUtil.query_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"客户合同资源不存在: {e.message}")
        return ResponseUtil.not_found_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取客户合同列表业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 其他异常，包装为业务异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取客户合同列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@contract_controller.get('/person/payments', summary="获取合同收支记录")
async def get_contract_payments(
    request: Request,
    contract_uuid: str = Query(..., description="合同UUID"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取合同收支记录接口

    根据合同UUID获取该合同的所有收支记录
    """
    try:
        # 调用服务层获取收支记录
        result = await ContractService.get_contract_payments_service(query_db, contract_uuid)

        return ResponseUtil.success(
            msg="获取合同收支记录成功",
            data=result
        )

    except QueryException as e:
        logger.error(f"获取合同收支记录查询异常: {e.message}")
        return ResponseUtil.query_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"合同收支记录资源不存在: {e.message}")
        return ResponseUtil.not_found_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取合同收支记录业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 其他异常，包装为业务异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取合同收支记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@contract_controller.post('/cash/order', summary="添加合同收支记录")
async def add_contract_payment(
    request: Request,
    payment_data: dict = Body(..., description="收支记录数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """添加合同收支记录接口

    为指定合同添加收支记录
    """
    try:
        # 调用服务层添加收支记录
        result = await ContractService.add_contract_payment_service(query_db, payment_data)

        return ResponseUtil.success(
            msg="添加合同收支记录成功",
            data=result
        )

    except QueryException as e:
        logger.error(f"添加合同收支记录查询异常: {e.message}")
        return ResponseUtil.query_error(msg=e.message, code=e.code, data=e.data)
    except ResourceNotFoundException as e:
        logger.error(f"合同收支记录资源不存在: {e.message}")
        return ResponseUtil.not_found_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"添加合同收支记录业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 其他异常，包装为业务异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"添加合同收支记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@contract_controller.post('/paper-images/upload/{contract_id}', summary="上传合同纸质图片")
async def upload_contract_paper_images(
    request: Request,
    contract_id: int = Path(..., description="合同ID"),
    files_data: dict = Body(..., description="文件数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """上传合同纸质图片接口

    为指定合同上传纸质合同图片
    """
    try:
        # 获取文件信息列表
        files = files_data.get('files', [])

        if not files:
            return ResponseUtil.error(msg="请选择要上传的图片")

        # 调用服务层上传图片
        result = await ContractService.upload_paper_contract_images_service(
            db=query_db,
            contract_id=contract_id,
            files=files
        )

        return ResponseUtil.success(
            msg="纸质合同图片上传成功",
            data=result
        )

    except ResourceNotFoundException as e:
        logger.error(f"合同不存在: {e.message}")
        return ResponseUtil.not_found_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"上传合同纸质图片业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except ValidationException as e:
        logger.error(f"上传合同纸质图片参数验证异常: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"上传合同纸质图片异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
