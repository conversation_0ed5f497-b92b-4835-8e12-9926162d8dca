from fastapi import APIRouter, Depends, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
from config.get_db import get_db
from module_admin.service.company_service import CompanyService
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.entity.vo.software_renewal_vo import (
    SoftwareRenewalRequest, SoftwareRenewalResponse,
    RenewalPreviewRequest, RenewalPreviewResponse
)
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
company_controller = APIRouter(prefix='/api/v1/company', dependencies=[Depends(AuthAdapter.get_current_user)])

@company_controller.get('/system-versions', summary="获取系统版本列表")
async def get_system_versions(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取系统版本列表接口

    获取所有可购买的系统版本，并根据公司关联信息显示购买状态

    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        系统版本列表数据
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 获取系统版本列表，公司: {company_uuid}")

        # 调用服务层获取系统版本列表
        result = await CompanyService.get_system_versions_service(query_db, company_uuid)

        return ResponseUtil.success(
            msg="获取系统版本列表成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取系统版本列表参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取系统版本列表查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取系统版本列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取系统版本列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@company_controller.get('/payment-info', summary="获取公司支付信息")
async def get_company_payment_info(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取公司支付方式列表和账户余额接口

    获取当前公司的支付方式列表和账户余额信息

    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        公司支付方式列表和账户余额数据
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 获取公司支付信息，公司: {company_uuid}")

        # 调用服务层获取公司支付信息
        result = await CompanyService.get_company_payment_info_service(query_db, company_uuid)

        return ResponseUtil.success(
            msg="获取公司支付信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"获取公司支付信息参数验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"获取公司支付信息查询失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取公司支付信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取公司支付信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@company_controller.post('/renewal/preview', summary="软件续费预览")
async def preview_software_renewal(
    request_data: RenewalPreviewRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """软件续费预览接口

    预览软件续费信息，包括费用计算、到期时间等，不实际执行续费

    Args:
        request_data: 续费预览请求数据
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        续费预览信息
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 预览软件续费，公司: {company_uuid}, 版本: {request_data.version_uuid}")

        # 调用服务层预览续费信息
        result = await CompanyService.preview_software_renewal_service(
            query_db, company_uuid, request_data.version_uuid, request_data.renewal_months
        )

        return ResponseUtil.success(
            msg="获取续费预览信息成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"软件续费预览验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"软件续费预览业务失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"软件续费预览异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@company_controller.post('/renewal/execute', summary="执行软件续费")
async def execute_software_renewal(
    request_data: SoftwareRenewalRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """执行软件续费接口

    执行软件续费操作，包括余额扣减、订单创建、有效期延长等

    Args:
        request_data: 续费请求数据
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        续费执行结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id
        operator_name = getattr(current_user.user, 'user_name', None) or getattr(current_user.user, 'username', '未知用户')

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 执行软件续费，公司: {company_uuid}, 版本: {request_data.version_uuid}")

        # 调用服务层执行续费
        result = await CompanyService.execute_software_renewal_service(
            query_db, company_uuid, request_data.version_uuid,
            request_data.renewal_months, current_user_id, operator_name, request_data.remark
        )

        return ResponseUtil.success(
            msg="软件续费成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"软件续费验证失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"软件续费业务失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"软件续费异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@company_controller.get('/check-version-expiry', summary="检查版本过期状态")
async def check_version_expiry(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """检查公司版本过期状态接口

    检查当前公司的所有软件版本是否过期，用于登录后的版本状态验证

    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        版本过期检查结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 检查版本过期状态，公司: {company_uuid}")

        # 调用服务层检查版本过期状态
        result = await CompanyService.check_company_version_expiry_service(query_db, company_uuid)

        return ResponseUtil.success(
            msg="检查版本过期状态成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"检查版本过期状态验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message)
    except BusinessException as e:
        logger.error(f"检查版本过期状态业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message)
    except Exception as e:
        logger.error(f"检查版本过期状态失败: {str(e)}")
        error_info = ExceptionUtil.get_exception_info(e)
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
