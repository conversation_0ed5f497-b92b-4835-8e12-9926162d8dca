import uuid
from datetime import timedel<PERSON>
import jwt
from fastapi import APIRouter, Body, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.enums import BusinessType, RedisInitKeyConfig
from config.env import AppConfig, JwtConfig
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.dao.internal_user_dao import InternalUserDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.internal_user_vo import CurrentInternalUserModel, ResetPasswordRequest, WechatBindRequest, WechatLoginRequest
from module_admin.service.internal_user_login_service import InternalLoginForm, InternalUserLoginService, oauth2_scheme
from utils.log_util import logger
from utils.response_util import ResponseUtil

# 使用统一的API路由前缀格式
internalLoginController = APIRouter(prefix='/api/v1/internal-login')



@internalLoginController.post('/login', response_model=CrudResponseModel, summary='内部用户登录', description='内部用户登录接口，返回token和session_id')
@Log(title='内部用户登录', business_type=BusinessType.OTHER, log_type='login')
async def internal_login(
    request: Request, form_data: InternalLoginForm = Depends(), query_db: AsyncSession = Depends(get_db)
):
    logger.info(f'开始内部用户登录，手机号: {form_data.mobile}')
    user = await InternalUserLoginService.authenticate_user(
        request=request,
        query_db=query_db,
        mobile=form_data.mobile,
        password=form_data.password,
        login_info=form_data.login_info
    )
    logger.info(f'用户验证成功，手机号: {form_data.mobile}')

    session_id = str(uuid.uuid4())
    logger.info(f'生成session_id: {session_id}')

    # 在异步上下文中获取用户ID
    user_id = str(user.id)
    access_token = await InternalUserLoginService.create_access_token(
        data={
            'user_id': user_id,
            'session_id': session_id
        },
        expires_delta=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
    )
    logger.info('创建token成功')

    if AppConfig.app_same_time_login:
        await request.app.state.redis.set(
            f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{session_id}',
            access_token,
            ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
        )
    else:
        await request.app.state.redis.set(
            f'{RedisInitKeyConfig.ACCESS_TOKEN.key}:{user_id}',
            access_token,
            ex=timedelta(minutes=JwtConfig.jwt_redis_expire_minutes)
        )
    logger.info('存储token成功')

    logger.info('内部用户登录完成')
    return ResponseUtil.success(msg='登录成功', data={'token': access_token, 'session_id': session_id})


@internalLoginController.get('/getInfo', response_model=CurrentInternalUserModel, summary='获取当前用户信息', description='获取当前登录用户的详细信息，包括用户基本信息和权限信息')
async def get_internal_user_info(
    request: Request, current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user)
):
    """获取当前登录用户信息"""
    logger.info('获取当前用户信息成功')
    return ResponseUtil.success(msg='获取当前用户信息成功', data=current_user)


@internalLoginController.post('/logout', summary='退出登录', description='用户退出登录，清除token和session信息')
async def internal_logout(request: Request, token: Optional[str] = Depends(oauth2_scheme)):
    payload = jwt.decode(
        token, JwtConfig.jwt_secret_key, algorithms=[JwtConfig.jwt_algorithm], options={'verify_exp': False}
    )
    session_id: str = payload.get('session_id')
    user_id: str = payload.get('user_id')
    await InternalUserLoginService.logout_services(request, session_id, user_id)
    logger.info('退出成功')
    return ResponseUtil.success(msg='退出成功')


@internalLoginController.post('/reset-password', response_model=CrudResponseModel, summary='重置密码', description='通过手机号和短信验证码重置密码')
async def reset_password(
    request: Request,
    reset_data: ResetPasswordRequest = Body(..., description="重置密码数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """重置密码接口

    通过手机号和短信验证码重置用户密码，无需token认证

    Args:
        request: Request对象
        reset_data: 重置密码数据
        query_db: 数据库会话

    Returns:
        重置结果
    """
    try:
        logger.info(f"收到重置密码请求，手机号: {reset_data.mobile}")
        logger.info(f"请求数据: mobile={reset_data.mobile}, verify_code={reset_data.verify_code}, new_password长度={len(reset_data.new_password)}, confirm_password长度={len(reset_data.confirm_password)}")

        # 验证确认密码
        if reset_data.new_password != reset_data.confirm_password:
            logger.warning("两次输入的密码不一致")
            return ResponseUtil.failure(msg="两次输入的密码不一致")

        # 调用服务层重置密码
        result = await InternalUserLoginService.reset_password_service(
            request=request,
            query_db=query_db,
            mobile=reset_data.mobile,
            verify_code=reset_data.verify_code,
            new_password=reset_data.new_password
        )

        if result:
            logger.info(f"密码重置成功，手机号: {reset_data.mobile}")
            return ResponseUtil.success(msg="密码重置成功")
        else:
            logger.warning(f"密码重置失败，手机号: {reset_data.mobile}")
            return ResponseUtil.failure(msg="密码重置失败")

    except Exception as e:
        logger.error(f"重置密码异常: {str(e)}")
        return ResponseUtil.error(msg=f"重置密码失败: {str(e)}")


@internalLoginController.post('/wechat-login', response_model=CrudResponseModel, summary='微信登录', description='通过微信授权码进行登录')
async def wechat_login(
    request: Request,
    wechat_data: WechatLoginRequest = Body(..., description="微信登录数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """微信登录接口

    通过微信授权码进行登录，如果用户未绑定则返回绑定信息

    Args:
        request: Request对象
        wechat_data: 微信登录数据
        query_db: 数据库会话

    Returns:
        登录结果或绑定信息
    """
    try:
        logger.info(f"收到微信登录请求，授权码: {wechat_data.code}")

        # 调用服务层微信登录
        result = await InternalUserLoginService.wechat_login_service(
            request=request,
            query_db=query_db,
            wx_code=wechat_data.code
        )

        if result.get('need_bind'):
            # 需要绑定手机号
            return ResponseUtil.success(
                msg="请绑定手机号完成登录",
                data=result
            )
        else:
            # 登录成功
            return ResponseUtil.success(
                msg="微信登录成功",
                data=result
            )

    except Exception as e:
        logger.error(f"微信登录异常: {str(e)}")
        return ResponseUtil.error(msg=f"微信登录失败: {str(e)}")


@internalLoginController.post('/wechat-bind', response_model=CrudResponseModel, summary='微信绑定', description='绑定微信到现有账号')
async def wechat_bind(
    request: Request,
    bind_data: WechatBindRequest = Body(..., description="微信绑定数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """微信绑定接口

    将微信账号绑定到现有的手机号账号

    Args:
        request: Request对象
        bind_data: 微信绑定数据
        query_db: 数据库会话

    Returns:
        绑定结果
    """
    try:
        logger.info(f"收到微信绑定请求，手机号: {bind_data.mobile}")

        # 调用服务层微信绑定
        result = await InternalUserLoginService.wechat_bind_service(
            request=request,
            query_db=query_db,
            mobile=bind_data.mobile,
            verify_code=bind_data.verify_code or "",
            wx_openid=bind_data.wx_openid,
            wx_unionid=bind_data.wx_unionid,
            wx_nickname=bind_data.wx_nickname,
            wx_avatar=bind_data.wx_avatar,
            skip_sms_verification=bind_data.skip_sms_verification or False
        )

        if result:
            return ResponseUtil.success(msg="微信绑定成功")
        else:
            return ResponseUtil.failure(msg="微信绑定失败")

    except Exception as e:
        logger.error(f"微信绑定异常: {str(e)}")
        return ResponseUtil.error(msg=f"微信绑定失败: {str(e)}")