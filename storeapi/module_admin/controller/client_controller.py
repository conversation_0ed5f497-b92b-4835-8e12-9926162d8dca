from fastapi import APIRouter, Depends, Query, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List
from module_admin.service.order_service import OrderService
from module_admin.service.client_service import ClientService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import BusinessException, QueryException, ResourceNotFoundException
from utils.log_util import logger
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.entity.vo.internal_user_vo import CurrentInternalUserModel
from sqlalchemy import text

# 使用统一的API路由前缀格式
router = APIRouter(
    prefix="/api/v1/client",
    tags=["客户端接口"],
    dependencies=[Depends(AuthAdapter.get_current_user)]
)

@router.post("/findServeOrderList")
async def find_serve_order_list(
    db: AsyncSession = Depends(get_db),
    page: str = Form("1"),
    size: str = Form("20"),
    status: Optional[str] = Form(None),
    commission_status: Optional[str] = Form(None)
):
    """
    获取服务订单列表

    :param db: 数据库会话
    :param page: 页码
    :param size: 每页数量
    :param status: 订单状态
    :param commission_status: 佣金状态
    :return: 订单列表和分页信息
    """
    try:
        # 转换参数类型
        page_num = int(page)
        page_size = int(size)

        # 调用服务层方法
        result = await OrderService.order_list_for_vue_service(
            db, page_num, page_size, status
        )

        return ResponseUtil.success(data=result)
    except QueryException as e:
        logger.error(f"获取服务订单列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取服务订单列表异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取服务订单列表失败: {str(e)}", code=500)

@router.post("/findServeOrderDetail")
async def find_serve_order_detail(
    db: AsyncSession = Depends(get_db),
    order_number: str = Form(...)
):
    """
    获取服务订单详情

    :param db: 数据库会话
    :param order_number: 订单编号
    :return: 订单详情
    """
    try:
        # 调用服务层方法
        result = await OrderService.order_detail_service(db, order_number)

        return ResponseUtil.success(data=result)
    except ResourceNotFoundException as e:
        logger.error(f"订单不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取服务订单详情异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取服务订单详情失败: {str(e)}", code=500)

@router.post("/updateServeOrder")
async def update_serve_order(
    db: AsyncSession = Depends(get_db),
    order_number: str = Form(...),
    service_personal: Optional[str] = Form(None),
    service_personal_commission: Optional[str] = Form(None),
    is_host: Optional[str] = Form(None),
    remark: Optional[str] = Form(None),
    service_remark: Optional[str] = Form(None),
    send_sms: Optional[str] = Form("0")
):
    """
    更新服务订单

    :param db: 数据库会话
    :param order_number: 订单编号
    :param service_personal: 服务人员ID
    :param service_personal_commission: 服务人员佣金
    :param is_host: 是否为主机
    :param remark: 备注
    :param service_remark: 服务备注
    :param send_sms: 是否发送短信
    :return: 更新结果
    """
    try:
        # 构建更新数据
        update_data = {
            "order_number": order_number
        }

        # 添加可选参数
        if service_personal is not None:
            update_data["service_personal"] = service_personal

        if service_personal_commission is not None:
            update_data["service_personal_commission"] = service_personal_commission

        if is_host is not None:
            update_data["is_host"] = is_host

        if remark is not None:
            update_data["remark"] = remark

        if service_remark is not None:
            update_data["service_remark"] = service_remark

        if send_sms is not None:
            update_data["send_sms"] = send_sms

        # 调用服务层方法
        await OrderService.update_serve_order_service(db, update_data)

        return ResponseUtil.success(data=True)
    except ResourceNotFoundException as e:
        logger.error(f"订单不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"更新服务订单业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"更新服务订单异常: {str(e)}")
        return ResponseUtil.error(msg=f"更新服务订单失败: {str(e)}", code=500)

@router.post("/createOrder")
async def create_order(
    db: AsyncSession = Depends(get_db),
    ext_isset: str = Form("1"),
    address: str = Form(...),
    lng: str = Form(...),
    lat: str = Form(...),
    address_desc: Optional[str] = Form(""),
    city: str = Form(...),
    user_name: str = Form(...),
    bd_city_name: Optional[str] = Form(""),
    mobile: str = Form(...),
    address_id: Optional[str] = Form(None),
    service_remark: Optional[str] = Form(""),
    store_uuid: str = Form(...),
    product_uuid: str = Form(...),
    buy_num: str = Form("1.0"),
    bd_province_name: Optional[str] = Form(""),
    address_user_id: Optional[str] = Form(None),
    bd_area_name: Optional[str] = Form(""),
    pay_actual: str = Form(...),
    after_sale_remark: Optional[str] = Form(""),
    pay_type: str = Form(...),
    is_fictitious_nub: Optional[str] = Form("0"),
    order_ext: Optional[str] = Form(None),
    send_sms: Optional[str] = Form("0")
):
    """
    创建订单

    :param db: 数据库会话
    :param ext_isset: 扩展信息是否设置标志
    :param address: 服务地址
    :param lng: 经度
    :param lat: 纬度
    :param address_desc: 地址描述
    :param city: 城市代码
    :param user_name: 用户名
    :param bd_city_name: 百度地图城市名称
    :param mobile: 用户手机号
    :param address_id: 地址ID
    :param service_remark: 服务备注
    :param store_uuid: 店铺唯一标识
    :param product_uuid: 产品唯一标识
    :param buy_num: 购买数量
    :param bd_province_name: 百度地图省份名称
    :param address_user_id: 地址用户ID
    :param bd_area_name: 百度地图区域名称
    :param pay_actual: 实际支付金额
    :param after_sale_remark: 售后备注
    :param pay_type: 支付类型
    :param is_fictitious_nub: 是否为虚拟号码
    :param order_ext: 订单扩展信息
    :param send_sms: 是否发送短信
    :return: 创建结果
    """
    try:
        # 构建订单数据
        order_data = {
            "ext_isset": ext_isset,
            "address": address,
            "lng": lng,
            "lat": lat,
            "address_desc": address_desc,
            "city": city,
            "user_name": user_name,
            "bd_city_name": bd_city_name,
            "mobile": mobile,
            "service_remark": service_remark,
            "store_uuid": store_uuid,
            "product_uuid": product_uuid,
            "buy_num": buy_num,
            "bd_province_name": bd_province_name,
            "bd_area_name": bd_area_name,
            "pay_actual": pay_actual,
            "after_sale_remark": after_sale_remark,
            "pay_type": pay_type,
            "is_fictitious_nub": is_fictitious_nub,
            "send_sms": send_sms
        }

        # 添加可选参数
        if address_id:
            order_data["address_id"] = address_id

        if address_user_id:
            order_data["address_user_id"] = address_user_id

        if order_ext:
            order_data["order_ext"] = order_ext

        # 调用服务层方法
        result = await OrderService.create_order_service(db, order_data)

        return ResponseUtil.success(data=result)
    except BusinessException as e:
        logger.error(f"创建订单业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"创建订单异常: {str(e)}")
        return ResponseUtil.error(msg=f"创建订单失败: {str(e)}", code=500)



@router.get("/scheduleList")
async def schedule_list(
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    date: Optional[str] = Query(None, description="日期")
):
    """
    获取排班列表

    :param db: 数据库会话
    :param page: 页码
    :param size: 每页数量
    :param date: 日期
    :return: 排班列表和分页信息
    """
    try:
        # 调用服务层方法
        from module_admin.service.aunt_service import AuntService
        result = await AuntService.find_aunt_schedule_list_service(
            db, page, size, date
        )

        return ResponseUtil.success(data=result)
    except QueryException as e:
        logger.error(f"获取排班列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取排班列表异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取排班列表失败: {str(e)}", code=500)

@router.post("/findClientList")
async def find_client_list(
    db: AsyncSession = Depends(get_db),
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    page: str = Form("1", description="页码"),
    size: str = Form("20", description="每页数量"),
    keyword: Optional[str] = Form(None, description="搜索关键词")
):
    """
    获取客户列表

    :param db: 数据库会话
    :param current_user: 当前用户信息
    :param page: 页码
    :param size: 每页数量
    :param keyword: 搜索关键词（客户姓名或手机号）
    :return: 客户列表和分页信息
    """
    try:
        # 转换参数类型
        page_num = int(page)
        page_size = int(size)

        # 调用服务层方法
        result = await ClientService.find_client_list_service(
            db, page_num, page_size, keyword, current_user
        )

        return ResponseUtil.success(data=result)
    except QueryException as e:
        logger.error(f"获取客户列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取客户列表业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取客户列表异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取客户列表失败: {str(e)}", code=500)

@router.post("/getUserBalance")
async def get_user_balance(
    db: AsyncSession = Depends(get_db),
    user_id: Optional[str] = Form(None)
):
    """
    获取用户余额

    :param db: 数据库会话
    :param user_id: 用户ID
    :return: 用户余额信息
    """
    try:
        # 调用服务层方法
        from module_admin.service.user_service import UserService
        result = await UserService.get_user_balance_service(db, user_id)

        return ResponseUtil.success(data=result)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取用户余额异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取用户余额失败: {str(e)}", code=500)

@router.post("/createClient")
async def create_client(
    db: AsyncSession = Depends(get_db),
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    name: str = Form(..., description="客户姓名"),
    mobile: str = Form(..., description="手机号"),
    address: Optional[str] = Form(None, description="地址"),
    lng: Optional[str] = Form(None, description="经度"),
    lat: Optional[str] = Form(None, description="纬度"),
    address_desc: Optional[str] = Form("", description="地址描述"),
    city: Optional[str] = Form("", description="城市"),
    province: Optional[str] = Form("", description="省份"),
    area: Optional[str] = Form("", description="区域")
):
    """
    创建新客户

    :param db: 数据库会话
    :param current_user: 当前用户信息
    :param name: 客户姓名
    :param mobile: 手机号
    :param address: 地址
    :param lng: 经度
    :param lat: 纬度
    :param address_desc: 地址描述
    :param city: 城市
    :param province: 省份
    :param area: 区域
    :return: 创建结果
    """
    try:
        # 构建客户数据
        client_data = {
            "name": name,
            "mobile": mobile,
            "address": address,
            "lng": lng,
            "lat": lat,
            "address_desc": address_desc,
            "city": city,
            "province": province,
            "area": area
        }

        # 调用服务层方法
        result = await ClientService.create_client_service(db, client_data, current_user)

        return ResponseUtil.success(data=result)
    except BusinessException as e:
        logger.error(f"创建客户业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"创建客户异常: {str(e)}")
        return ResponseUtil.error(msg=f"创建客户失败: {str(e)}", code=500)

@router.post("/getClientAddresses")
async def get_client_addresses(
    db: AsyncSession = Depends(get_db),
    current_user: CurrentInternalUserModel = Depends(InternalUserLoginService.get_current_user),
    client_id: str = Form(..., description="客户ID")
):
    """
    获取客户地址列表

    :param db: 数据库会话
    :param current_user: 当前用户信息
    :param client_id: 客户ID
    :return: 地址列表
    """
    try:
        # 调用服务层方法
        result = await ClientService.get_client_addresses_service(db, client_id, current_user)

        return ResponseUtil.success(data=result)
    except BusinessException as e:
        logger.error(f"获取客户地址业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取客户地址异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取客户地址失败: {str(e)}", code=500)

@router.post("/updateClientVip")
async def update_client_vip(
    db: AsyncSession = Depends(get_db),
    client_id: str = Form(...),
    is_vip: str = Form("1")
):
    """
    更新客户会员状态

    :param db: 数据库会话
    :param client_id: 客户ID
    :param is_vip: 会员状态，1表示是会员，0表示非会员
    :return: 更新结果
    """
    try:
        # 构建SQL更新语句
        query = """
            UPDATE ccuser
            SET is_vip = :is_vip
            WHERE id = :client_id AND is_delete = '0'
        """

        # 执行更新
        result = await db.execute(
            text(query),
            {"client_id": client_id, "is_vip": is_vip}
        )

        # 提交事务
        await db.commit()

        # 检查是否有行被更新
        if result.rowcount == 0:
            raise ResourceNotFoundException(message=f"客户ID {client_id} 不存在或已被删除")

        return ResponseUtil.success(data=True)
    except ResourceNotFoundException as e:
        logger.error(f"客户不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"更新客户会员状态异常: {str(e)}")
        return ResponseUtil.error(msg=f"更新客户会员状态失败: {str(e)}", code=500)
