from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.article_service import ArticleService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
article_controller = APIRouter(prefix='/api/v1/article', dependencies=[Depends(AuthAdapter.get_current_user)])

@article_controller.get('/getArticleList', summary="获取文章列表")
async def get_article_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取文章列表接口
    
    根据条件查询文章列表，支持分页和关键词搜索
    
    原始API路径: /article/getArticleList
    """
    try:
        # 调用服务层获取文章列表
        result = await ArticleService.get_article_list_service(query_db, page, size, keywords)
        
        return ResponseUtil.success(
            msg="获取文章列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询文章列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取文章列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取文章列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@article_controller.get('/findArticleList', summary="获取文章列表")
async def find_article_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    keywords: Optional[str] = Query(None, description="关键词"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取文章列表接口
    
    根据条件查询文章列表，支持分页和关键词搜索
    
    原始API路径: /article/findArticleList
    """
    try:
        # 调用服务层获取文章列表
        result = await ArticleService.find_article_list_service(query_db, page, size, keywords)
        
        return ResponseUtil.success(
            msg="获取文章列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询文章列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取文章列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取文章列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@article_controller.post('/createArticle', summary="创建文章")
async def create_article(
    request: Request,
    title: str = Form(..., description="文章标题"),
    content: str = Form(..., description="文章内容"),
    author: Optional[str] = Form(None, description="作者"),
    category: Optional[str] = Form(None, description="分类"),
    tags: Optional[str] = Form(None, description="标签"),
    cover_image: Optional[str] = Form(None, description="封面图片"),
    status: Optional[str] = Form("1", description="状态"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """创建文章接口
    
    创建新的文章
    
    原始API路径: /article/createArticle
    """
    try:
        # 构建文章数据
        article_data = {
            "title": title,
            "content": content,
            "author": author,
            "category": category,
            "tags": tags,
            "cover_image": cover_image,
            "status": status
        }
        
        # 调用服务层创建文章
        result = await ArticleService.create_article_service(query_db, article_data)
        
        return ResponseUtil.success(
            msg="创建文章成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"创建文章数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"创建文章业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建文章异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
