from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.data_service import DataService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, BusinessException

# 使用统一的API路由前缀格式
data_controller = APIRouter(prefix='/api/v1/data', dependencies=[Depends(AuthAdapter.get_current_user)])

@data_controller.get('/getCity', summary="获取城市数据")
async def get_city(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取城市数据接口
    
    获取城市的层级数据，包括省、市、区县
    
    原始API路径: /data/getCity
    """
    try:
        # 调用服务层获取城市数据
        result = await DataService.get_city_service(query_db)
        
        return ResponseUtil.success(
            msg="获取城市数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询城市数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取城市数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取城市数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@data_controller.get('/getMetaData', summary="获取元数据")
async def get_meta_data(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取元数据接口
    
    获取系统的元数据，包括各种枚举值和配置信息
    
    原始API路径: /data/getMetaData
    """
    try:
        # 调用服务层获取元数据
        result = await DataService.get_meta_data_service(query_db)
        
        return ResponseUtil.success(
            msg="获取元数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询元数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取元数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取元数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@data_controller.get('/getLowMetaData', summary="获取低层级元数据")
async def get_low_meta_data(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取低层级元数据接口
    
    获取系统的低层级元数据，包括各种细分的枚举值和配置信息
    
    原始API路径: /data/getLowMetaData
    """
    try:
        # 调用服务层获取低层级元数据
        result = await DataService.get_low_meta_data_service(query_db)
        
        return ResponseUtil.success(
            msg="获取低层级元数据成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询低层级元数据失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取低层级元数据业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取低层级元数据异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@data_controller.get('/getNation', summary="获取民族列表")
async def get_nation(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取民族列表接口
    
    获取中国的民族列表数据
    
    原始API路径: /data/getNation
    """
    try:
        # 调用服务层获取民族列表
        result = await DataService.get_nation_service(query_db)
        
        return ResponseUtil.success(
            msg="获取民族列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询民族列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取民族列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取民族列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@data_controller.get('/getBank', summary="获取银行信息")
async def get_bank(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取银行信息接口
    
    获取银行列表数据
    
    原始API路径: /data/getBank
    """
    try:
        # 调用服务层获取银行信息
        result = await DataService.get_bank_service(query_db)
        
        return ResponseUtil.success(
            msg="获取银行信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询银行信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取银行信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取银行信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@data_controller.get('/getVersion', summary="获取版本信息")
async def get_version(
    request: Request,
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取版本信息接口
    
    获取系统的版本信息
    
    原始API路径: /data/getVersion
    """
    try:
        # 调用服务层获取版本信息
        result = await DataService.get_version_service(query_db)
        
        return ResponseUtil.success(
            msg="获取版本信息成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询版本信息失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取版本信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取版本信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
