from fastapi import APIRouter, Depends, Request, Query, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.customer_service import CustomerService
from module_admin.service.auth_adapter import AuthAdapter
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
customer_controller = APIRouter(prefix='/api/v1/custom', dependencies=[Depends(AuthAdapter.get_current_user)])

# 创建一个新的路由器用于客户验证（不同的前缀）
customer_validate_controller = APIRouter(prefix='/api/v1/customer', dependencies=[Depends(AuthAdapter.get_current_user)])

@customer_controller.get('/findDemandList', summary="获取需求列表")
async def find_demand_list(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    user_uuid: Optional[str] = Query(None, description="用户UUID"),
    live_home: Optional[str] = Query(None, description="是否住家"),
    store_uuid: Optional[str] = Query(None, description="门店UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取需求列表接口
    
    获取客户需求列表，支持分页和筛选
    
    原始API路径: /custom/findDemandList
    """
    try:
        # 调用服务层获取需求列表
        result = await CustomerService.find_demand_list_service(query_db, page, size, user_uuid, live_home, store_uuid)
        
        return ResponseUtil.success(
            msg="获取需求列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询需求列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取需求列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取需求列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@customer_controller.get('/findDemand', summary="获取需求详情")
async def find_demand(
    request: Request,
    uuid: str = Query(..., description="需求UUID"),
    is_monitor_user_behavior: Optional[str] = Query(None, description="是否监控用户行为"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取需求详情接口
    
    根据UUID获取客户需求的详细信息
    
    原始API路径: /custom/findDemand
    """
    try:
        # 调用服务层获取需求详情
        result = await CustomerService.find_demand_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取需求详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"需求资源不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code, data=e.data)
    except QueryException as e:
        logger.error(f"查询需求详情失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取需求详情业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取需求详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@customer_controller.post('/addDemand', summary="添加需求")
async def add_demand(
    request: Request,
    wechat_number: Optional[str] = Form("", description="微信号"),
    mobile: Optional[str] = Form("", description="手机号"),
    work_demands: Optional[str] = Form("", description="工作需求描述"),
    remark: Optional[str] = Form("", description="备注"),
    salary_unit: Optional[str] = Form("每月", description="薪资单位"),
    source: str = Form(..., description="需求来源"),
    user_uuid: str = Form(..., description="用户唯一标识"),
    max_salary: Optional[str] = Form("", description="最高薪资"),
    work_demands_time_format: Optional[str] = Form("", description="工作需求时间格式"),
    aunt_type: str = Form(..., description="阿姨类型"),
    name: Optional[str] = Form("未命名", description="用户姓名"),
    min_salary: Optional[str] = Form("", description="最低薪资"),
    vacation: Optional[str] = Form("", description="休假安排"),
    can_live_home: Optional[str] = Form("0", description="是否可住家"),
    work_demands_time: Optional[str] = Form("", description="工作需求时间"),
    city_id: Optional[str] = Form("0", description="城市ID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """添加需求接口
    
    此接口用于添加新的服务需求。
    
    原始API路径: /custom/addDemand
    """
    try:
        # 构建需求数据
        demand_data = {
            "wechat_number": wechat_number,
            "mobile": mobile,
            "work_demands": work_demands,
            "remark": remark,
            "salary_unit": salary_unit,
            "source": source,
            "user_uuid": user_uuid,
            "max_salary": max_salary,
            "work_demands_time_format": work_demands_time_format,
            "aunt_type": aunt_type,
            "name": name,
            "min_salary": min_salary,
            "vacation": vacation,
            "can_live_home": can_live_home,
            "work_demands_time": work_demands_time,
            "city_id": city_id
        }
        
        # 调用服务层添加需求
        result = await CustomerService.add_demand_service(query_db, demand_data)
        
        return ResponseUtil.success(
            msg="添加需求成功",
            data=result
        )
    except ValidationException as e:
        logger.error(f"添加需求数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"添加需求业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"添加需求异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@customer_controller.get('/findDemandFollow', summary="查询需求跟进记录")
async def find_demand_follow(
    request: Request,
    uuid: str = Query(..., description="需求UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """查询需求跟进记录接口
    
    根据需求UUID获取需求的跟进记录列表
    
    原始API路径: /custom/findDemandFollow
    """
    try:
        # 调用服务层获取需求跟进记录
        result = await CustomerService.find_demand_follow_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取需求跟进记录成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询需求跟进记录失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取需求跟进记录业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取需求跟进记录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@customer_controller.get('/findDemandContractList', summary="查询需求合同列表")
async def find_demand_contract_list(
    request: Request,
    uuid: str = Query(..., description="需求UUID"),
    xmjz_time: Optional[str] = Query(None, description="系统时间戳"),
    query_db: AsyncSession = Depends(get_db)
):
    """查询需求合同列表接口
    
    根据需求UUID获取需求的合同列表
    
    原始API路径: /custom/findDemandContractList
    """
    try:
        # 调用服务层获取需求合同列表
        result = await CustomerService.find_demand_contract_list_service(query_db, uuid)
        
        return ResponseUtil.success(
            msg="获取需求合同列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"查询需求合同列表失败: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取需求合同列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取需求合同列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@customer_validate_controller.post('/validate', summary="验证客户是否存在")
async def validate_customer(
    request: Request,
    customer_data: Dict[str, Any] = Body(..., description="客户验证数据"),
    query_db: AsyncSession = Depends(get_db)
):
    """验证客户是否存在接口

    根据客户姓名和手机号验证客户是否存在于数据库中
    """
    try:
        # 调用服务层验证客户
        result = await CustomerService.validate_customer_service(query_db, customer_data)

        return ResponseUtil.success(
            msg="客户验证成功",
            data=result
        )

    except ValidationException as e:
        # 验证异常，直接向上传递
        logger.error(f"客户验证数据验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        # 业务异常，直接向上传递
        logger.error(f"客户验证业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 其他异常，包装为业务异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"客户验证异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
