from fastapi import APIRouter, Depends, UploadFile, File, Form, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from config.get_db import get_db
from utils.file_upload_util import FileUploadUtil
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import InternalUserLoginService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import BusinessException

# 使用统一的API路由前缀格式
# 文件控制器（包含需要token验证和不需要token验证的路由）
file_controller = APIRouter(prefix='/api/v1/file')

@file_controller.post('/upload', summary="上传文件")
async def upload_file(
    request: Request,
    file: UploadFile = File(..., description="文件"),
    route: Optional[str] = Form(None, description="文件路径"),
    remark: Optional[str] = Form(None, description="备注信息，用于file_main表的remark字段"),
    query_db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """上传文件接口

    上传文件到云存储

    原始API路径: /file/upload
    """
    try:
        # 获取当前用户信息
        user_name = current_user.user.name or 'unknown'

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 上传文件
        file_info = await file_upload_util.upload_file(
            file=file,
            file_name=file.filename,
            route=route,
            created_by=user_name,
            remark=remark,
            db=query_db
        )

        return ResponseUtil.success(
            msg="文件上传成功",
            data={
                "id": file_info.get("id"),  # 添加数据库记录ID
                "file_name": file_info["file_name"],
                "file_url": file_info["file_url"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"]
            }
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"文件上传异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@file_controller.post('/upload-base64', summary="上传Base64文件", dependencies=[Depends(AuthAdapter.get_current_user)])
async def upload_base64_file(
    request: Request,
    base64_str: str = Form(..., description="Base64字符串"),
    file_name: str = Form(..., description="文件名"),
    route: Optional[str] = Form(None, description="文件路径"),
    query_db: AsyncSession = Depends(get_db)
):
    """上传Base64文件接口

    上传Base64编码的文件到云存储

    原始API路径: /file/upload-base64
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 上传Base64文件
        file_info = await file_upload_util.upload_base64(
            base64_str=base64_str,
            file_name=file_name,
            route=route,
            created_by=current_user.get("user_name", "system"),
            db=query_db
        )

        return ResponseUtil.success(
            msg="文件上传成功",
            data={
                "file_name": file_info["file_name"],
                "file_url": file_info["file_url"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"]
            }
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"文件上传异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@file_controller.get('/get-url', summary="获取文件URL", dependencies=[Depends(AuthAdapter.get_current_user)])
async def get_file_url(
    request: Request,
    object_key: str = Query(..., description="对象键")
):
    """获取文件URL接口

    根据对象键获取文件URL

    原始API路径: /file/get-url
    """
    try:
        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 获取文件URL
        file_url = file_upload_util.get_file_url(object_key)

        return ResponseUtil.success(
            msg="获取文件URL成功",
            data={"file_url": file_url}
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取文件URL异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@file_controller.post('/upload-from-url', summary="从URL上传文件", dependencies=[Depends(AuthAdapter.get_current_user)])
async def upload_from_url(
    request: Request,
    url: str = Form(..., description="文件URL"),
    file_name: Optional[str] = Form(None, description="文件名称"),
    route: Optional[str] = Form(None, description="文件路径"),
    query_db: AsyncSession = Depends(get_db)
):
    """从URL上传文件接口

    从URL下载文件并上传到云存储

    原始API路径: /file/upload-from-url
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 从URL上传文件
        file_info = await file_upload_util.upload_from_url(
            url=url,
            file_name=file_name,
            route=route,
            created_by=current_user.get("user_name", "system"),
            db=query_db
        )

        return ResponseUtil.success(
            msg="从URL上传文件成功",
            data={
                "file_name": file_info["file_name"],
                "file_url": file_info["file_url"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"]
            }
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"从URL上传文件异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@file_controller.post('/batch-upload', summary="批量上传文件", dependencies=[Depends(AuthAdapter.get_current_user)])
async def batch_upload_file(
    request: Request,
    files: List[UploadFile] = File(..., description="文件列表"),
    route: Optional[str] = Form(None, description="文件路径"),
    query_db: AsyncSession = Depends(get_db)
):
    """批量上传文件接口

    批量上传文件到云存储

    原始API路径: /file/batch-upload
    """
    try:
        # 获取当前用户信息
        current_user = await AuthAdapter.get_current_user(request)

        # 检查文件数量限制
        MAX_FILES = 10
        if len(files) > MAX_FILES:
            return ResponseUtil.error(msg=f"批量上传文件数量超过限制，最多允许 {MAX_FILES} 个文件")

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 批量上传文件，使用事务保证原子性
        result_list = []
        uploaded_files = []  # 记录已上传的文件，用于回滚

        try:
            for i, file in enumerate(files):
                logger.info(f"正在上传第 {i+1}/{len(files)} 个文件: {file.filename}")

                file_info = await file_upload_util.upload_file(
                    file=file,
                    file_name=file.filename,
                    route=route,
                    created_by=current_user.get("user_name", "system"),
                    db=query_db
                )

                uploaded_files.append(file_info)
                result_list.append({
                    "file_name": file_info["file_name"],
                    "file_url": file_info["file_url"],
                    "file_size": file_info["file_size"],
                    "file_type": file_info["file_type"]
                })

            # 所有文件上传成功，提交事务
            await query_db.commit()

            return ResponseUtil.success(
                msg="批量上传文件成功",
                data=result_list
            )

        except Exception as upload_error:
            # 上传过程中出错，回滚事务
            await query_db.rollback()
            logger.error(f"批量上传失败，已回滚事务: {str(upload_error)}")
            raise upload_error
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"批量上传文件异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


# 不需要token验证的文件上传接口
@file_controller.post('/public/upload', summary="公共文件上传（无需token验证）")
async def public_upload_file(
    file: UploadFile = File(..., description="文件"),
    route: Optional[str] = Form(None, description="文件路径"),
    remark: Optional[str] = Form(None, description="备注信息，用于file_main表的remark字段"),
    query_db: AsyncSession = Depends(get_db)
):
    """公共文件上传接口（无需token验证）

    上传文件到云存储，不需要token验证，默认是管理员上传

    原始API路径: /public-file/upload
    """
    try:
        # 安全检查：文件大小限制（5MB）
        MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
        if file.size and file.size > MAX_FILE_SIZE:
            return ResponseUtil.error(msg=f"文件大小超过限制，最大允许 {MAX_FILE_SIZE // (1024*1024)}MB")

        # 安全检查：文件类型限制
        ALLOWED_EXTENSIONS = {'jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'}
        if file.filename:
            file_ext = file.filename.rsplit('.', 1)[-1].lower()
            if file_ext not in ALLOWED_EXTENSIONS:
                return ResponseUtil.error(msg=f"不支持的文件类型: {file_ext}")

        # 安全检查：文件名验证
        if not file.filename or len(file.filename) > 255:
            return ResponseUtil.error(msg="文件名无效")

        # 使用默认的管理员用户名
        admin_user = {"user_name": "admin"}

        # 添加日志输出，帮助调试
        logger.info(f"开始公共文件上传，文件名: {file.filename}, 路径: {route}")

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 上传文件
        file_info = await file_upload_util.upload_file(
            file=file,
            file_name=file.filename,
            route=route,
            created_by=admin_user.get("user_name", "admin"),
            remark=remark,
            db=query_db
        )

        # 添加日志输出，帮助调试
        logger.info(f"公共文件上传成功，文件信息: {file_info}")

        return ResponseUtil.success(
            msg="文件上传成功",
            data={
                "id": file_info.get("id"),  # 添加数据库记录ID
                "file_name": file_info["file_name"],
                "file_url": file_info["file_url"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"]
            }
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"公共文件上传异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@file_controller.post('/public/upload-base64', summary="公共Base64文件上传（无需token验证）")
async def public_upload_base64_file(
    base64_str: str = Form(..., description="Base64字符串"),
    file_name: str = Form(..., description="文件名"),
    route: Optional[str] = Form(None, description="文件路径"),
    query_db: AsyncSession = Depends(get_db)
):
    """公共Base64文件上传接口（无需token验证）

    上传Base64编码的文件到云存储，不需要token验证，默认是管理员上传

    原始API路径: /public-file/upload-base64
    """
    try:
        # 使用默认的管理员用户名
        admin_user = {"user_name": "admin"}

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 上传Base64文件
        file_info = await file_upload_util.upload_base64(
            base64_str=base64_str,
            file_name=file_name,
            route=route,
            created_by=admin_user.get("user_name", "admin"),
            db=query_db
        )

        return ResponseUtil.success(
            msg="文件上传成功",
            data={
                "file_name": file_info["file_name"],
                "file_url": file_info["file_url"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"]
            }
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"公共Base64文件上传异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@file_controller.post('/upload-to-cloud-only', summary="只上传到云存储（不创建数据库记录）")
async def upload_to_cloud_only(
    file: UploadFile = File(..., description="文件"),
    fileName: Optional[str] = Form(None, description="文件名"),
    route: Optional[str] = Form(None, description="文件路径")
):
    """只上传到云存储，不创建数据库记录

    用于合同图片上传等场景，避免重复创建数据库记录
    """
    try:
        logger.info(f"开始云存储上传，文件名: {file.filename}, 自定义文件名: {fileName}")

        # 创建文件上传工具类实例
        file_upload_util = FileUploadUtil()

        # 使用自定义文件名或原文件名
        final_file_name = fileName if fileName else file.filename

        # 上传文件（不传递db参数，避免创建数据库记录）
        file_info = await file_upload_util.upload_file(
            file=file,
            file_name=final_file_name,
            route=route or "publicImage",
            created_by="admin",
            db=None  # 不传递数据库会话，避免创建数据库记录
        )

        logger.info(f"云存储上传成功，文件信息: {file_info}")

        return ResponseUtil.success(
            msg="文件上传成功",
            data={
                "file_name": file_info["file_name"],
                "file_url": file_info["file_url"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"]
            }
        )
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"云存储上传异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
