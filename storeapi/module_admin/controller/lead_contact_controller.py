"""
线索接单控制器
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.log_util import logger
from module_admin.service.lead_contact_service import LeadContactService
from module_admin.entity.vo.lead_contact_vo import (
    LeadContactRequest,
    LeadContactResponse
)

# 创建路由器
router = APIRouter(prefix="/api/v1/lead", tags=["线索接单"])


@router.post("/contact", response_model=LeadContactResponse, summary="线索接单")
async def handle_lead_contact(
    request: Request,
    contact_data: LeadContactRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    处理线索接单请求
    
    Args:
        request: FastAPI请求对象
        contact_data: 接单请求数据
        db: 数据库会话
        
    Returns:
        接单结果
    """
    try:
        logger.info(f"收到线索接单请求: lead_id={contact_data.lead_id}")
        
        # 调用服务层处理接单逻辑
        result = await LeadContactService.handle_lead_contact(db, contact_data)
        
        logger.info(f"线索接单处理完成: {result}")
        
        return ResponseUtil.success(data=result, msg=result.message)

    except Exception as e:
        logger.error(f"线索接单请求处理失败: {str(e)}")
        return ResponseUtil.error(msg=str(e))


@router.get("/contact/status/{lead_id}", summary="获取线索接单状态")
async def get_lead_contact_status(
    lead_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取线索接单状态
    
    Args:
        lead_id: 线索ID
        db: 数据库会话
        
    Returns:
        接单状态信息
    """
    try:
        logger.info(f"查询线索接单状态: lead_id={lead_id}")
        
        # 调用服务层获取状态
        status_info = await LeadContactService.get_lead_contact_status(db, lead_id)
        
        return ResponseUtil.success(data=status_info, msg="获取状态成功")

    except Exception as e:
        logger.error(f"获取线索接单状态失败: {str(e)}")
        return ResponseUtil.error(msg=str(e))


@router.post("/test/phone", summary="测试微信手机号获取")
async def test_get_phone_number(
    wx_code: str
):
    """
    测试微信手机号获取功能
    
    Args:
        wx_code: 微信授权码
        
    Returns:
        手机号获取结果
    """
    try:
        logger.info(f"测试微信手机号获取: wx_code={wx_code}")
        
        # 调用服务层获取手机号
        phone_number = await LeadContactService.get_phone_number_from_wechat(wx_code)
        
        if phone_number:
            return ResponseUtil.success(
                data={"phone_number": phone_number},
                msg="获取手机号成功"
            )
        else:
            return ResponseUtil.error(msg="获取手机号失败")

    except Exception as e:
        logger.error(f"测试微信手机号获取失败: {str(e)}")
        return ResponseUtil.error(msg=str(e))
