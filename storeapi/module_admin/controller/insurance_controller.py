"""
保险管理控制器
包含：
- 保险购买接口
- 保险产品查询
- 保险订单状态查询
"""
from fastapi import APIRouter, Depends, Body, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from config.get_db import get_db
from module_admin.service.insurance_service import InsuranceService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from exceptions.exception import (
    BusinessException, ValidationException, DatabaseException,
    ResourceNotFoundException, AuthException
)
from utils.exception_util import ExceptionUtil


# 请求模型定义
class InsurancePurchaseRequest(BaseModel):
    """保险购买请求模型"""
    contract_id: str = Field(..., description="合同ID")
    contract_uuid: str = Field(..., description="合同UUID")
    product_info: Dict[str, Any] = Field(..., description="产品信息")
    policyholder_info: Dict[str, str] = Field(..., description="投保人信息")
    effective_date: str = Field(..., description="生效日期")


class InsuranceProductsResponse(BaseModel):
    """保险产品响应模型"""
    id: int = Field(..., description="产品ID")
    name: str = Field(..., description="产品名称")
    price: str = Field(..., description="价格")
    description: str = Field(..., description="产品描述")
    coverage: str = Field(..., description="保障范围")
    company: str = Field(..., description="承保公司")
    work_type: int = Field(..., description="工作类型")
    risk_code: str = Field(..., description="风险代码")
    payment_type: str = Field(..., description="付费类型")


# 创建保险管理API路由
insurance_controller = APIRouter(
    prefix="/api/v1/insurance"
)


@insurance_controller.post(
    '/purchase',
    summary="购买保险"
)
async def purchase_insurance(
    request: InsurancePurchaseRequest,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """购买保险接口
    
    根据传入的产品信息和投保人信息，调用金正云保险接口进行投保
    
    Args:
        request: 保险购买请求数据
        db: 数据库会话
        current_user: 当前登录用户信息
    
    Returns:
        购买结果，包含支付链接或保单信息
    """
    try:
        # 获取当前用户的公司UUID和门店UUID
        company_uuid = current_user.user.company_id
        store_uuid = current_user.user.store_uuid
        
        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")
        
        logger.info(f"用户 {current_user.user.name} 购买保险，产品: {request.product_info.get('name')}")
        
        # 调用服务层处理保险购买
        result = await InsuranceService.purchase_insurance_service(
            db=db,
            company_uuid=company_uuid,
            store_uuid=store_uuid,
            contract_id=request.contract_id,
            contract_uuid=request.contract_uuid,
            product_info=request.product_info,
            policyholder_info=request.policyholder_info,
            effective_date=request.effective_date
        )
        
        return ResponseUtil.success(data=result, msg="保险购买请求提交成功")
        
    except ValidationException as e:
        logger.warning(f"保险购买参数验证失败: {str(e)}")
        return ResponseUtil.failure(msg=str(e))
    except BusinessException as e:
        logger.error(f"保险购买业务异常: {str(e)}")
        return ResponseUtil.failure(msg=str(e))
    except Exception as e:
        logger.error(f"保险购买系统异常: {str(e)}")
        return ResponseUtil.failure(msg="保险购买失败，请稍后重试")


@insurance_controller.get(
    '/products',
    summary="获取保险产品列表"
)
async def get_insurance_products(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取保险产品列表接口
    
    返回可用的保险产品列表
    
    Args:
        db: 数据库会话
        current_user: 当前登录用户信息
    
    Returns:
        保险产品列表
    """
    try:
        logger.info("获取保险产品列表")
        
        # 调用服务层获取产品列表
        products = await InsuranceService.get_insurance_products_service(db)
        
        return ResponseUtil.success(data=products, msg="获取保险产品列表成功")
        
    except Exception as e:
        logger.error(f"获取保险产品列表失败: {str(e)}")
        return ResponseUtil.failure(msg="获取保险产品列表失败")


@insurance_controller.get(
    '/order/status',
    summary="获取保险订单状态"
)
async def get_insurance_order_status(
    order_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取保险订单状态接口
    
    Args:
        order_id: 订单ID
        db: 数据库会话
        current_user: 当前登录用户信息
    
    Returns:
        订单状态信息
    """
    try:
        logger.info(f"查询保险订单状态: {order_id}")
        
        # 调用服务层查询订单状态
        status = await InsuranceService.get_order_status_service(db, order_id)
        
        return ResponseUtil.success(data=status, msg="获取订单状态成功")
        
    except ResourceNotFoundException as e:
        logger.warning(f"订单不存在: {order_id}")
        return ResponseUtil.failure(msg="订单不存在")
    except Exception as e:
        logger.error(f"获取订单状态失败: {str(e)}")
        return ResponseUtil.failure(msg="获取订单状态失败")


@insurance_controller.get(
    '/history',
    summary="获取保险历史记录"
)
async def get_insurance_history(
    page: int = 1,
    size: int = 10,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取保险历史记录接口
    
    Args:
        page: 页码
        size: 每页数量
        db: 数据库会话
        current_user: 当前登录用户信息
    
    Returns:
        保险历史记录列表
    """
    try:
        # 获取当前用户的门店UUID
        store_uuid = current_user.user.store_uuid
        
        logger.info(f"获取门店 {store_uuid} 的保险历史记录")
        
        # 调用服务层获取历史记录
        history = await InsuranceService.get_insurance_history_service(
            db, store_uuid, page, size
        )
        
        return ResponseUtil.success(data=history, msg="获取保险历史记录成功")
        
    except Exception as e:
        logger.error(f"获取保险历史记录失败: {str(e)}")
        return ResponseUtil.failure(msg="获取保险历史记录失败")


@insurance_controller.get(
    '/policy/detail',
    summary="获取保单详情"
)
async def get_policy_detail(
    policy_id: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """获取保单详情接口
    
    Args:
        policy_id: 保单ID
        db: 数据库会话
        current_user: 当前登录用户信息
    
    Returns:
        保单详情信息
    """
    try:
        logger.info(f"获取保单详情: {policy_id}")
        
        # 调用服务层获取保单详情
        policy = await InsuranceService.get_policy_detail_service(db, policy_id)
        
        return ResponseUtil.success(data=policy, msg="获取保单详情成功")
        
    except ResourceNotFoundException as e:
        logger.warning(f"保单不存在: {policy_id}")
        return ResponseUtil.failure(msg="保单不存在")
    except Exception as e:
        logger.error(f"获取保单详情失败: {str(e)}")
        return ResponseUtil.failure(msg="获取保单详情失败")
