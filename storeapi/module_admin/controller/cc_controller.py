from fastapi import APIRouter, Depends, Query, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from module_admin.service.cc_service import CCService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import BusinessException, QueryException, ResourceNotFoundException
from utils.log_util import logger
from module_admin.service.auth_adapter import AuthAdapter

# 使用统一的API路由前缀格式
router = APIRouter(
    prefix="/api/v1/cc",
    tags=["CC接口"],
    dependencies=[Depends(AuthAdapter.get_current_user)]
)

@router.post("/appointment/getAppointmentTime")
async def get_appointment_time(
    db: AsyncSession = Depends(get_db),
    start_search_date: str = Form(...),
    store_uuid: str = Form(...),
    serve_number: Optional[str] = Form(None)
):
    """
    获取预约时间

    :param db: 数据库会话
    :param start_search_date: 开始搜索日期
    :param store_uuid: 店铺UUID
    :param serve_number: 服务单号
    :return: 预约时间列表
    """
    try:
        # 调用服务层方法
        result = await CCService.get_appointment_time_service(
            db, start_search_date, store_uuid, serve_number
        )

        return ResponseUtil.success(data=result)
    except BusinessException as e:
        logger.error(f"获取预约时间业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取预约时间异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取预约时间失败: {str(e)}", code=500)

@router.post("/serviceSkillMain/getAll")
async def get_service_skill_main_all(
    db: AsyncSession = Depends(get_db)
):
    """
    获取所有服务技能主分类

    :param db: 数据库会话
    :return: 服务技能主分类列表
    """
    try:
        # 调用服务层方法
        result = await CCService.get_service_skill_main_all_service(db)

        return ResponseUtil.success(data=result)
    except QueryException as e:
        logger.error(f"获取服务技能主分类查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取服务技能主分类异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取服务技能主分类失败: {str(e)}", code=500)


@router.get("/getCcUserBalance")
async def get_cc_user_balance(
    db: AsyncSession = Depends(get_db),
    user_id: str = Query(..., description="用户ID")
):
    """
    获取CC用户余额

    :param db: 数据库会话
    :param user_id: 用户ID
    :return: 用户余额信息
    """
    try:
        # 调用服务层方法
        result = await CCService.get_cc_user_balance_service(db, user_id)

        return ResponseUtil.success(data=result)
    except ResourceNotFoundException as e:
        logger.error(f"用户不存在: {e.message}")
        return ResponseUtil.resource_not_found(msg=e.message, code=e.code)
    except QueryException as e:
        logger.error(f"获取用户余额查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取用户余额异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取用户余额失败: {str(e)}", code=500)
