"""
微信公众号相关数据访问层
"""
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from typing import Optional, Dict, Any
from datetime import datetime
from utils.log_util import logger
from module_admin.entity.do.internal_user import InternalUser


class WechatOfficialDao:
    """微信公众号数据访问层"""

    @staticmethod
    async def get_user_wechat_bind_info(db: AsyncSession, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户微信公众号绑定信息
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            用户微信绑定信息字典，如果未绑定则返回None
        """
        try:
            # 查询用户的微信公众号绑定信息
            stmt = select(
                InternalUser.wx_official_openid,
                InternalUser.wx_official_unionid,
                InternalUser.wx_official_nickname,
                InternalUser.wx_official_avatar,
                InternalUser.wx_official_bind_time
            ).where(
                and_(
                    InternalUser.uuid == user_id,
                    InternalUser.wx_official_openid.isnot(None),
                    InternalUser.wx_official_openid != ''
                )
            )
            
            result = await db.execute(stmt)
            row = result.first()
            
            if row:
                return {
                    'wx_official_openid': row.wx_official_openid,
                    'wx_official_unionid': row.wx_official_unionid,
                    'wx_official_nickname': row.wx_official_nickname,
                    'wx_official_avatar': row.wx_official_avatar,
                    'bind_time': row.wx_official_bind_time.isoformat() if row.wx_official_bind_time else None
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取用户微信绑定信息失败: {str(e)}")
            raise e

    @staticmethod
    async def bind_wechat_to_user(
        db: AsyncSession, 
        user_id: str, 
        openid: str,
        unionid: Optional[str] = None,
        nickname: Optional[str] = None,
        avatar: Optional[str] = None
    ) -> bool:
        """
        绑定微信到用户
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            openid: 微信openid
            unionid: 微信unionid
            nickname: 微信昵称
            avatar: 微信头像
            
        Returns:
            绑定是否成功
        """
        try:
            # 更新用户的微信公众号信息（使用专用字段，不影响小程序登录）
            stmt = update(InternalUser).where(
                InternalUser.uuid == user_id
            ).values(
                wx_official_openid=openid,
                wx_official_unionid=unionid,
                wx_official_nickname=nickname,
                wx_official_avatar=avatar,
                wx_official_bind_time=datetime.now(),
                update_time=datetime.now()
            )
            
            result = await db.execute(stmt)
            await db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            logger.error(f"绑定微信到用户失败: {str(e)}")
            await db.rollback()
            raise e

    @staticmethod
    async def get_user_by_openid(db: AsyncSession, openid: str) -> Optional[InternalUser]:
        """
        根据微信公众号openid获取用户信息

        Args:
            db: 数据库会话
            openid: 微信公众号openid

        Returns:
            用户信息，如果不存在则返回None
        """
        try:
            stmt = select(InternalUser).where(InternalUser.wx_official_openid == openid)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"根据公众号openid获取用户信息失败: {str(e)}")
            raise e

    @staticmethod
    async def check_openid_exists(db: AsyncSession, openid: str) -> bool:
        """
        检查公众号openid是否已存在

        Args:
            db: 数据库会话
            openid: 微信公众号openid

        Returns:
            是否存在
        """
        try:
            stmt = select(InternalUser.id).where(InternalUser.wx_official_openid == openid)
            result = await db.execute(stmt)
            return result.scalar_one_or_none() is not None

        except Exception as e:
            logger.error(f"检查公众号openid是否存在失败: {str(e)}")
            raise e

    @staticmethod
    async def get_user_wechat_bind_info_by_phone(db: AsyncSession, phone: str) -> Optional[Dict[str, Any]]:
        """
        通过手机号获取用户微信公众号绑定信息

        Args:
            db: 数据库会话
            phone: 用户手机号

        Returns:
            用户微信绑定信息字典，如果未绑定则返回None
        """
        try:
            # 查询用户的微信公众号绑定信息
            stmt = select(
                InternalUser.wx_official_openid,
                InternalUser.wx_official_unionid,
                InternalUser.wx_official_nickname,
                InternalUser.wx_official_avatar,
                InternalUser.wx_official_bind_time
            ).where(
                and_(
                    InternalUser.mobile == phone,
                    InternalUser.wx_official_openid.isnot(None),
                    InternalUser.wx_official_openid != ''
                )
            )

            result = await db.execute(stmt)
            row = result.first()

            if row:
                return {
                    'wx_official_openid': row.wx_official_openid,
                    'wx_official_unionid': row.wx_official_unionid,
                    'wx_official_nickname': row.wx_official_nickname,
                    'wx_official_avatar': row.wx_official_avatar,
                    'bind_time': row.wx_official_bind_time.isoformat() if row.wx_official_bind_time else None
                }

            return None

        except Exception as e:
            logger.error(f"通过手机号获取用户微信绑定信息失败: {str(e)}")
            raise e

    @staticmethod
    async def unbind_wechat_from_user(db: AsyncSession, user_id: str) -> bool:
        """
        解绑用户的微信公众号

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            解绑是否成功
        """
        try:
            # 清空用户的微信公众号信息
            stmt = update(InternalUser).where(
                InternalUser.uuid == user_id
            ).values(
                wx_official_openid=None,
                wx_official_unionid=None,
                wx_official_nickname=None,
                wx_official_avatar=None,
                wx_official_bind_time=None,
                update_time=datetime.now()
            )

            result = await db.execute(stmt)
            await db.commit()

            return result.rowcount > 0

        except Exception as e:
            logger.error(f"解绑微信公众号失败: {str(e)}")
            await db.rollback()
            raise e
