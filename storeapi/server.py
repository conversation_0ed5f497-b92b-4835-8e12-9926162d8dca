import os
import time
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from config.env import AppConfig
from config.get_db import init_create_table
from config.get_redis import RedisUtil
from config.get_scheduler import SchedulerUtil
from exceptions.handle import handle_exception
from middlewares.handle import handle_middleware
# 导入新的模型以确保表被创建
from module_admin.model.customer_follow import CustomerFollow
from module_admin.entity.do.demand_square import DemandSquare
# 移除不需要的控制器导入
from module_admin.controller.common_controller import commonController
from module_admin.controller.config_controller import configController
# 移除不需要的控制器导入
# from module_admin.controller.dept_controller import deptController
from module_admin.controller.dict_controller import dictController
from module_admin.controller.log_controller import logController
from module_admin.controller.job_controller import jobController
from module_admin.controller.menu_controller import menuController
# from module_admin.controller.notice_controller import noticeController
# from module_admin.controller.online_controller import onlineController
# from module_admin.controller.post_controler import postController
from module_admin.controller.role_controller import roleController
# from module_admin.controller.server_controller import serverController
from sub_applications.handle import handle_sub_applications
from utils.common_util import worship
from utils.log_util import logger
from module_admin.controller.internal_user_controller import internalUserController
from module_admin.controller.internal_login_controller import internalLoginController
# 导入新控制器
from module_admin.controller.auth_controller import auth_controller
from module_admin.controller.aunt_controller import aunt_controller
from module_admin.controller.article_controller import article_controller
from module_admin.controller.company_controller import company_controller
from module_admin.controller.data_controller import data_controller
from module_admin.controller.order_controller import order_controller
from module_admin.controller.customer_controller import customer_controller, customer_validate_controller
from module_admin.controller.recruit_controller import recruit_controller
from module_admin.controller.evaluation_controller import evaluation_controller
from module_admin.controller.training_controller import training_controller
from module_admin.controller.public_training_controller import public_training_controller
from module_admin.controller.microsite_controller import microsite_controller
from module_admin.controller.poster_controller import poster_controller
from module_admin.controller.video_controller import video_controller
from module_admin.controller.user_controller import user_controller
from module_admin.controller.merge_controller import merge_controller
from module_admin.controller.help_controller import help_controller
from module_admin.controller.file_controller import file_controller
from module_admin.controller.client_controller import router as client_controller
from module_admin.controller.cc_controller import router as cc_controller
from module_admin.controller.data_controller import data_controller
from module_admin.controller.auth_controller import auth_controller
from module_admin.controller.experience_controller import experience_controller
from module_admin.controller.promotion_code_controller import promotion_code_controller, promotion_code_public_controller
from module_admin.controller.staff_controller import staff_controller
from module_admin.controller.product_controller import product_controller
from module_admin.controller.skill_controller import skill_controller
from module_admin.controller.service_staff_login_controller import router as service_staff_login_controller
from module_admin.controller.staff_order_controller import staff_order_controller
from module_admin.controller.wechat_event_controller import wechatEventController
from module_admin.controller.public_controller import public_controller
from module_admin.controller.sales_controller import salesController
from module_admin.controller.sister_business_controller import sister_business_controller
from module_admin.controller.contract_controller import contract_controller
from module_admin.controller.lead_contact_controller import router as lead_contact_controller
from module_admin.controller.insurance_controller import insurance_controller
from module_admin.controller.payment_controller import payment_controller
from module_admin.controller.add_account_controller import add_account_controller
from module_admin.controller.store_controller import store_controller
from module_admin.controller.demand_square_controller import demand_square_controller
from module_admin.controller.yeepay_innet_controller import yeepay_innet_controller
from module_admin.controller.account_application_controller import account_application_controller
from module_admin.controller.staff_account_application_controller import staff_account_application_controller
from module_admin.controller.area_code_controller import area_code_controller
from module_admin.controller.ocr_controller import ocr_controller
from module_admin.controller.staff_ocr_controller import staff_ocr_controller
from module_admin.controller.staff_common_controller import staff_common_controller
from module_admin.controller.announcement_controller import router as announcement_controller


# 设置时区为北京时间（UTC+8）
os.environ['TZ'] = 'Asia/Shanghai'
# tzset() 在 Windows 上不可用，只在 Unix/Linux 系统上调用
if hasattr(time, 'tzset'):
    time.tzset()

# 生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f'{AppConfig.app_name}开始启动')
    # 确保时区设置生效
    logger.info(f'当前时区设置: {time.tzname}')
    worship()
    await init_create_table()
    app.state.redis = await RedisUtil.create_redis_pool()
    # 初始化字典缓存
    await RedisUtil.init_sys_dict(app.state.redis)
    await RedisUtil.init_sys_config(app.state.redis)
    # await SchedulerUtil.init_system_scheduler()
    logger.info(f'{AppConfig.app_name}启动成功')
    yield
    await RedisUtil.close_redis_pool(app)
    # await SchedulerUtil.close_system_scheduler()





# 初始化FastAPI对象
app = FastAPI(
    title=AppConfig.app_name,
    description=f'{AppConfig.app_name}接口文档',
    version=AppConfig.app_version,
    lifespan=lifespan,
    docs_url=None,  # 禁用默认的 docs_url
    redoc_url=None,  # 禁用默认的 redoc_url
)



# 挂载子应用
handle_sub_applications(app)
# 加载中间件处理方法
handle_middleware(app)
# 加载全局异常处理方法
handle_exception(app)

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=AppConfig.app_name,
        version=AppConfig.app_version,
        description=f'{AppConfig.app_name}接口文档',
        routes=app.routes,
    )

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# 设置自定义 OpenAPI schema
app.openapi = custom_openapi


# 自定义 Swagger UI 路由
@app.get("/jingang/store/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=f"/openapi.json",
        title=f"{AppConfig.app_name} - Swagger UI",
        oauth2_redirect_url=None,
        swagger_js_url="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
        swagger_css_url="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css",
        swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        swagger_ui_parameters={
            "docExpansion": "none",
            "defaultModelsExpandDepth": 8,
            "displayOperationId": True,
            "filter": True,
            "deepLinking": True,
            "persistAuthorization": True,
        }
    )


# 加载路由列表
controller_list = [
    # 使用内部用户登录替代普通用户登录
    {'router': internalLoginController, 'tags': ['内部用户登录模块']},
    {'router': roleController, 'tags': ['系统管理-角色管理']},
    {'router': menuController, 'tags': ['系统管理-菜单管理']},
    {'router': dictController, 'tags': ['系统管理-字典管理']},
    {'router': configController, 'tags': ['系统管理-参数管理']},
    {'router': logController, 'tags': ['系统管理-日志管理']},
    {'router': jobController, 'tags': ['系统监控-定时任务']},
    {'router': commonController, 'tags': ['通用模块']},
    {'router': internalUserController, 'tags': ['内部用户管理']},
    # 新增控制器
    {'router': auth_controller, 'tags': ['认证模块']},
    {'router': aunt_controller, 'tags': ['阿姨管理模块']},
    {'router': article_controller, 'tags': ['文章管理模块']},
    {'router': company_controller, 'tags': ['公司管理模块']},
    {'router': data_controller, 'tags': ['数据管理模块']},
    {'router': order_controller, 'tags': ['订单管理模块']},
    {'router': customer_controller, 'tags': ['客户管理模块']},
    {'router': customer_validate_controller, 'tags': ['客户验证模块']},
    {'router': recruit_controller, 'tags': ['招聘管理模块']},
    {'router': evaluation_controller, 'tags': ['评价管理模块']},
    {'router': training_controller, 'tags': ['培训管理模块']},
    {'router': public_training_controller, 'tags': ['公开培训接口模块']},
    {'router': microsite_controller, 'tags': ['微站点管理模块']},
    {'router': poster_controller, 'tags': ['海报管理模块']},
    {'router': video_controller, 'tags': ['短视频管理模块']},
    {'router': user_controller, 'tags': ['用户管理模块']},
    {'router': merge_controller, 'tags': ['合单管理模块']},
    {'router': help_controller, 'tags': ['帮助管理模块']},
    {'router': file_controller, 'tags': ['文件管理模块']},
    {'router': client_controller, 'tags': ['客户端接口模块']},
    {'router': cc_controller, 'tags': ['CC接口模块']},
    {'router': data_controller, 'tags': ['数据接口模块']},
    {'router': auth_controller, 'tags': ['认证接口模块']},
    {'router': experience_controller, 'tags': ['体验注册模块']},
    {'router': promotion_code_controller, 'tags': ['推广码管理模块']},
    {'router': promotion_code_public_controller, 'tags': ['推广码公共接口']},
    {'router': staff_controller, 'tags': ['服务员工管理模块']},
    {'router': product_controller, 'tags': ['产品管理模块']},
    {'router': skill_controller, 'tags': ['技能管理模块']},
    {'router': service_staff_login_controller, 'tags': ['员工登录模块']},
    {'router': staff_order_controller, 'tags': ['员工端订单模块']},
    {'router': wechatEventController, 'tags': ['微信公众号事件模块']},
    {'router': public_controller, 'tags': ['公共接口模块']},
    {'router': salesController, 'tags': ['销售管理模块']},
    {'router': sister_business_controller, 'tags': ['三嫂业务模块']},
    {'router': contract_controller, 'tags': ['合同管理模块']},
    {'router': lead_contact_controller, 'tags': ['线索接单模块']},
    {'router': insurance_controller, 'tags': ['保险管理模块']},
    {'router': payment_controller, 'tags': ['支付管理模块']},
    {'router': add_account_controller, 'tags': ['加账号管理模块']},
    {'router': store_controller, 'tags': ['门店管理模块']},
    {'router': demand_square_controller, 'tags': ['需求广场模块']},
    {'router': yeepay_innet_controller, 'tags': ['易宝支付入网模块']},
    {'router': account_application_controller, 'tags': ['开户申请管理模块']},
    {'router': staff_account_application_controller, 'tags': ['员工端开户申请模块']},
    {'router': area_code_controller, 'tags': ['省市区编码管理模块']},
    {'router': ocr_controller, 'tags': ['OCR识别模块']},
    {'router': staff_ocr_controller, 'tags': ['员工端OCR识别模块']},
    {'router': staff_common_controller, 'tags': ['员工端通用模块']},
    {'router': announcement_controller, 'tags': ['公告管理模块']},
]

for controller in controller_list:
    app.include_router(router=controller.get('router'), tags=controller.get('tags'))
